'use client';

import React, { useState, useEffect } from 'react';
import { TradingStrategy } from '@/components/backtesting/StrategySelector';
import { ImprovedBacktestResults } from '@/components/backtesting/ImprovedBacktestResults';
import { ApexTradingChart } from '@/components/backtesting/ApexTradingChart';
import { PageHeader } from '@/components/PageHeader';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  ArrowLeft,
  Play,
  BarChart3,
  Target,
  Activity,
  TrendingUp,
  Zap,
  Clock
} from 'lucide-react';

// Available trading strategies
// TO ADD NEW STRATEGY:
// 1. Add strategy object here with unique ID
// 2. Add API endpoint mapping in runBacktestForStrategy function
// 3. Strategy cards will automatically show REAL data from API calls
const strategies: TradingStrategy[] = [
  {
    id: 'multi-timeframe-ada',
    name: 'Multi-Timeframe ADA Strategy',
    description: 'Advanced multi-timeframe analysis with RSI and momentum indicators for ADA trading',
    timeframe: '15m',
    type: 'technical',
    status: 'active',
    performance: {
      winRate: 66.67,
      totalTrades: 45,
      profitFactor: 2.30,
      avgReturn: 60.8, // 2736.12 / 45 trades = 60.8 ADA per trade
      maxDrawdown: 8.2
    },
    features: ['RSI Analysis', 'Multi-Timeframe', 'Momentum Detection', 'Risk Management'],
    icon: <BarChart3 className="w-5 h-5" />
  },
  {
    id: 'fibonacci-retracement',
    name: 'Fibonacci Retracement Strategy',
    description: 'Professional 15-minute Fibonacci analysis with real-time swing point detection',
    timeframe: '15m',
    type: 'technical',
    status: 'active',
    performance: {
      winRate: 71.0,
      totalTrades: 31,
      profitFactor: 2.1,
      avgReturn: 61.0, // 1890.45 / 31 trades = 61.0 ADA per trade
      maxDrawdown: 5.2
    },
    features: ['Dynamic Fibonacci Levels', 'Swing Point Detection', 'Real-time Analysis', 'Support/Resistance'],
    icon: <Target className="w-5 h-5" />
  }
];

// Sample backtest data for different strategies
const strategyResults = {
  'multi-timeframe-ada': {
    runId: 'backtest_MultiTimeframeADAStrategy_e77532aed0a80',
    strategy: 'Multi-Timeframe ADA Strategy',
    symbol: 'ADAUSD',
    timeframe: '15m',
    startDate: '2025-04-01T00:00:00Z',
    endDate: '2025-06-30T23:59:59Z',
    totalNetPnl: 2736.12,
    winRate: 66.67,
    maxDrawdown: 8.20,
    sharpeRatio: 2.30,
    totalTrades: 45,
    avgTradeDuration: 480, // 8 hours in minutes
  trades: [
    {
      id: 'trade_1',
      entryTime: '2025-06-23T09:15:00Z',
      exitTime: '2025-06-23T15:30:00Z',
      side: 'SHORT' as const,
      entryPrice: 0.5805,
      exitPrice: 0.5652,
      size: 86206.90, // $50000 / 0.5805
      netPnl: 1314.49,
      reason: 'RSI extremely oversold (20-)',
      duration: 375
    },
    {
      id: 'trade_2',
      entryTime: '2025-06-23T16:00:00Z',
      exitTime: '2025-06-23T18:45:00Z',
      side: 'SHORT' as const,
      entryPrice: 0.5652,
      exitPrice: 0.5638,
      size: 88495.58,
      netPnl: 125.89,
      reason: 'RSI extremely oversold (20-)',
      duration: 165
    },
    {
      id: 'trade_3',
      entryTime: '2025-06-24T08:30:00Z',
      exitTime: '2025-06-25T20:30:00Z',
      side: 'SHORT' as const,
      entryPrice: 0.5638,
      exitPrice: 0.5581,
      size: 88707.48,
      netPnl: 506.94,
      reason: 'Trend max time (36h)',
      duration: 2160
    },
    {
      id: 'trade_4',
      entryTime: '2025-06-25T21:00:00Z',
      exitTime: '2025-06-26T02:15:00Z',
      side: 'SHORT' as const,
      entryPrice: 0.5581,
      exitPrice: 0.5612,
      size: 89634.12,
      netPnl: -380.43,
      reason: 'Stop loss hit',
      duration: 315
    },
    {
      id: 'trade_5',
      entryTime: '2025-06-26T10:30:00Z',
      exitTime: '2025-06-26T18:30:00Z',
      side: 'SHORT' as const,
      entryPrice: 0.5623,
      exitPrice: 0.5611,
      size: 88945.23,
      netPnl: 105.73,
      reason: 'Momentum max time (8h)',
      duration: 480
    },
    {
      id: 'trade_6',
      entryTime: '2025-06-26T19:00:00Z',
      exitTime: '2025-06-27T01:45:00Z',
      side: 'SHORT' as const,
      entryPrice: 0.5611,
      exitPrice: 0.5673,
      size: 89123.45,
      netPnl: -610.30,
      reason: 'Stop loss hit',
      duration: 405
    },
    {
      id: 'trade_7',
      entryTime: '2025-06-27T11:15:00Z',
      exitTime: '2025-06-27T19:15:00Z',
      side: 'SHORT' as const,
      entryPrice: 0.5680,
      exitPrice: 0.5636,
      size: 88028.17,
      netPnl: 384.44,
      reason: 'Momentum max time (8h)',
      duration: 480
    },
    {
      id: 'trade_8',
      entryTime: '2025-06-28T09:00:00Z',
      exitTime: '2025-06-28T17:00:00Z',
      side: 'SHORT' as const,
      entryPrice: 0.5636,
      exitPrice: 0.5649,
      size: 88707.48,
      netPnl: -120.21,
      reason: 'Momentum max time (8h)',
      duration: 480
    },
    {
      id: 'trade_9',
      entryTime: '2025-06-28T20:30:00Z',
      exitTime: '2025-06-29T04:30:00Z',
      side: 'SHORT' as const,
      entryPrice: 0.5649,
      exitPrice: 0.5559,
      size: 88523.67,
      netPnl: 796.97,
      reason: 'Momentum profit target (80%)',
      duration: 480
    },
    {
      id: 'trade_10',
      entryTime: '2025-06-29T12:15:00Z',
      exitTime: '2025-06-29T18:45:00Z',
      side: 'SHORT' as const,
      entryPrice: 0.5559,
      exitPrice: 0.5624,
      size: 89945.23,
      netPnl: -993.71,
      reason: 'Stop loss hit',
      duration: 390
    },
    {
      id: 'trade_11',
      entryTime: '2025-06-30T08:00:00Z',
      exitTime: '2025-06-30T14:30:00Z',
      side: 'LONG' as const,
      entryPrice: 0.5670,
      exitPrice: 0.5809,
      size: 88183.42,
      netPnl: 1227.09,
      reason: 'Take profit hit',
      duration: 390
    },
    {
      id: 'trade_12',
      entryTime: '2025-06-30T15:00:00Z',
      exitTime: '2025-06-30T23:59:00Z',
      side: 'SHORT' as const,
      entryPrice: 0.5809,
      exitPrice: 0.5862,
      size: 86123.45,
      netPnl: -456.78,
      reason: 'End of backtest period',
      duration: 539
    }
  ],
  chartData: [
    // Comprehensive 15-minute chart data showing detailed price action between trades
    // Pre-trade setup (June 23, 2025 - before first SHORT entry at 09:15)
    { time: '2025-06-23T08:00:00Z', open: 0.5780, high: 0.5795, low: 0.5775, close: 0.5790 },
    { time: '2025-06-23T08:15:00Z', open: 0.5790, high: 0.5800, low: 0.5785, close: 0.5795 },
    { time: '2025-06-23T08:30:00Z', open: 0.5795, high: 0.5810, low: 0.5790, close: 0.5805 },
    { time: '2025-06-23T08:45:00Z', open: 0.5805, high: 0.5815, low: 0.5800, close: 0.5810 },
    { time: '2025-06-23T09:00:00Z', open: 0.5810, high: 0.5820, low: 0.5800, close: 0.5805 },

    // First trade period: SHORT entry at 09:15 ($0.5805) to exit at 15:30 ($0.5652)
    { time: '2025-06-23T09:15:00Z', open: 0.5805, high: 0.5810, low: 0.5800, close: 0.5802 }, // Entry candle
    { time: '2025-06-23T09:30:00Z', open: 0.5802, high: 0.5808, low: 0.5795, close: 0.5798 },
    { time: '2025-06-23T09:45:00Z', open: 0.5798, high: 0.5805, low: 0.5790, close: 0.5792 },
    { time: '2025-06-23T10:00:00Z', open: 0.5792, high: 0.5800, low: 0.5785, close: 0.5788 },
    { time: '2025-06-23T10:15:00Z', open: 0.5788, high: 0.5795, low: 0.5780, close: 0.5782 },
    { time: '2025-06-23T10:30:00Z', open: 0.5782, high: 0.5790, low: 0.5775, close: 0.5778 },
    { time: '2025-06-23T10:45:00Z', open: 0.5778, high: 0.5785, low: 0.5770, close: 0.5772 },
    { time: '2025-06-23T11:00:00Z', open: 0.5772, high: 0.5780, low: 0.5765, close: 0.5768 },
    { time: '2025-06-23T11:15:00Z', open: 0.5768, high: 0.5775, low: 0.5760, close: 0.5762 },
    { time: '2025-06-23T11:30:00Z', open: 0.5762, high: 0.5770, low: 0.5755, close: 0.5758 },
    { time: '2025-06-23T11:45:00Z', open: 0.5758, high: 0.5765, low: 0.5750, close: 0.5752 },
    { time: '2025-06-23T12:00:00Z', open: 0.5752, high: 0.5760, low: 0.5745, close: 0.5748 },
    { time: '2025-06-23T12:15:00Z', open: 0.5748, high: 0.5755, low: 0.5740, close: 0.5742 },
    { time: '2025-06-23T12:30:00Z', open: 0.5742, high: 0.5750, low: 0.5735, close: 0.5738 },
    { time: '2025-06-23T12:45:00Z', open: 0.5738, high: 0.5745, low: 0.5730, close: 0.5732 },
    { time: '2025-06-23T13:00:00Z', open: 0.5732, high: 0.5740, low: 0.5725, close: 0.5728 },
    { time: '2025-06-23T13:15:00Z', open: 0.5728, high: 0.5735, low: 0.5720, close: 0.5722 },
    { time: '2025-06-23T13:30:00Z', open: 0.5722, high: 0.5730, low: 0.5715, close: 0.5718 },
    { time: '2025-06-23T13:45:00Z', open: 0.5718, high: 0.5725, low: 0.5710, close: 0.5712 },
    { time: '2025-06-23T14:00:00Z', open: 0.5712, high: 0.5720, low: 0.5705, close: 0.5708 },
    { time: '2025-06-23T14:15:00Z', open: 0.5708, high: 0.5715, low: 0.5700, close: 0.5702 },
    { time: '2025-06-23T14:30:00Z', open: 0.5702, high: 0.5710, low: 0.5695, close: 0.5698 },
    { time: '2025-06-23T14:45:00Z', open: 0.5698, high: 0.5705, low: 0.5690, close: 0.5692 },
    { time: '2025-06-23T15:00:00Z', open: 0.5692, high: 0.5700, low: 0.5685, close: 0.5688 },
    { time: '2025-06-23T15:15:00Z', open: 0.5688, high: 0.5695, low: 0.5680, close: 0.5682 },
    { time: '2025-06-23T15:30:00Z', open: 0.5682, high: 0.5690, low: 0.5650, close: 0.5652 }, // Exit candle

    // Between trades: Brief consolidation before second SHORT entry at 16:00 ($0.5652)
    { time: '2025-06-23T15:45:00Z', open: 0.5652, high: 0.5660, low: 0.5648, close: 0.5655 },
    { time: '2025-06-23T16:00:00Z', open: 0.5655, high: 0.5658, low: 0.5650, close: 0.5652 }, // Second entry
    { time: '2025-06-23T16:15:00Z', open: 0.5652, high: 0.5655, low: 0.5645, close: 0.5648 },
    { time: '2025-06-23T16:30:00Z', open: 0.5648, high: 0.5652, low: 0.5640, close: 0.5642 },
    { time: '2025-06-23T16:45:00Z', open: 0.5642, high: 0.5648, low: 0.5635, close: 0.5638 },
    { time: '2025-06-23T17:00:00Z', open: 0.5638, high: 0.5642, low: 0.5632, close: 0.5635 },
    { time: '2025-06-23T17:15:00Z', open: 0.5635, high: 0.5640, low: 0.5630, close: 0.5632 },
    { time: '2025-06-23T17:30:00Z', open: 0.5632, high: 0.5638, low: 0.5628, close: 0.5630 },
    { time: '2025-06-23T17:45:00Z', open: 0.5630, high: 0.5635, low: 0.5625, close: 0.5628 },
    { time: '2025-06-23T18:00:00Z', open: 0.5628, high: 0.5632, low: 0.5622, close: 0.5625 },
    { time: '2025-06-23T18:15:00Z', open: 0.5625, high: 0.5630, low: 0.5620, close: 0.5622 },
    { time: '2025-06-23T18:30:00Z', open: 0.5622, high: 0.5628, low: 0.5618, close: 0.5620 },
    { time: '2025-06-23T18:45:00Z', open: 0.5620, high: 0.5625, low: 0.5615, close: 0.5618 } // Second exit around here
  ] // Detailed 15-minute candles showing all price action between trades
  },
  'fibonacci-retracement': {
    runId: 'backtest_FibonacciStrategy_f88643bed1b91',
    strategy: 'Fibonacci Retracement Strategy',
    symbol: 'ADAUSD',
    timeframe: '15m',
    startDate: '2025-04-01T00:00:00Z',
    endDate: '2025-06-30T23:59:59Z',
    totalNetPnl: 1890.45,
    winRate: 71.0,
    maxDrawdown: 5.2,
    sharpeRatio: 2.1,
    totalTrades: 31,
    avgTradeDuration: 420, // 7 hours in minutes
    trades: [
      {
        id: 'fib_trade_1',
        entryTime: '2025-06-15T10:30:00Z',
        exitTime: '2025-06-15T16:45:00Z',
        side: 'LONG' as const,
        entryPrice: 0.7421, // 61.8% Fibonacci support
        exitPrice: 0.7563, // 23.6% Fibonacci resistance
        size: 67340.25,
        netPnl: 956.32,
        reason: 'Bounce from 61.8% Fibonacci support',
        duration: 375
      },
      {
        id: 'fib_trade_2',
        entryTime: '2025-06-18T14:15:00Z',
        exitTime: '2025-06-18T19:30:00Z',
        side: 'SHORT' as const,
        entryPrice: 0.7563, // 23.6% Fibonacci resistance
        exitPrice: 0.7465, // 50% Fibonacci support
        size: 66225.17,
        netPnl: 649.07,
        reason: 'Rejection at 23.6% Fibonacci resistance',
        duration: 315
      },
      {
        id: 'fib_trade_3',
        entryTime: '2025-06-22T09:00:00Z',
        exitTime: '2025-06-22T15:15:00Z',
        side: 'LONG' as const,
        entryPrice: 0.7319, // 38.2% Fibonacci support
        exitPrice: 0.7509, // Near 50% Fibonacci level
        size: 68450.89,
        netPnl: 1300.57,
        reason: 'Strong bounce from 38.2% Fibonacci support',
        duration: 375
      }
    ],
    chartData: [
      // Fibonacci-based chart data showing key levels
      { time: '2025-06-15T08:00:00Z', open: 0.7450, high: 0.7465, low: 0.7435, close: 0.7440 },
      { time: '2025-06-15T08:15:00Z', open: 0.7440, high: 0.7445, low: 0.7425, close: 0.7430 },
      { time: '2025-06-15T08:30:00Z', open: 0.7430, high: 0.7435, low: 0.7420, close: 0.7425 },
      { time: '2025-06-15T08:45:00Z', open: 0.7425, high: 0.7430, low: 0.7415, close: 0.7421 },
      { time: '2025-06-15T09:00:00Z', open: 0.7421, high: 0.7425, low: 0.7418, close: 0.7420 },
      // Entry at 61.8% support
      { time: '2025-06-15T10:30:00Z', open: 0.7420, high: 0.7425, low: 0.7418, close: 0.7421 },
      { time: '2025-06-15T10:45:00Z', open: 0.7421, high: 0.7435, low: 0.7420, close: 0.7430 },
      { time: '2025-06-15T11:00:00Z', open: 0.7430, high: 0.7445, low: 0.7428, close: 0.7440 },
      // Move towards 23.6% resistance
      { time: '2025-06-15T15:30:00Z', open: 0.7555, high: 0.7565, low: 0.7550, close: 0.7560 },
      { time: '2025-06-15T15:45:00Z', open: 0.7560, high: 0.7568, low: 0.7558, close: 0.7563 },
      // Exit at 23.6% resistance
      { time: '2025-06-15T16:45:00Z', open: 0.7563, high: 0.7565, low: 0.7560, close: 0.7563 }
    ]
  }
};

export default function BacktestResultsPage() {
  const [selectedStrategy, setSelectedStrategy] = useState<TradingStrategy | null>(null);
  const [isRunningBacktest, setIsRunningBacktest] = useState(false);
  const [backtestResults, setBacktestResults] = useState<any>(null);
  const [realStrategyData, setRealStrategyData] = useState<{[key: string]: any}>({});
  const [currentTime, setCurrentTime] = useState<string>('');
  const [isMounted, setIsMounted] = useState(false);

  // Handle client-side mounting and time updates
  useEffect(() => {
    setIsMounted(true);
    const updateTime = () => {
      setCurrentTime(new Date().toLocaleTimeString());
    };

    updateTime(); // Initial time
    const interval = setInterval(updateTime, 1000); // Update every second

    return () => clearInterval(interval);
  }, []);

  const handleStrategySelect = async (strategy: TradingStrategy) => {
    setSelectedStrategy(strategy);
    setBacktestResults(null);

    // Auto-run backtest when strategy is selected
    if (strategy.status !== 'coming-soon') {
      await runBacktestForStrategy(strategy);
    }
  };

  const runBacktestForStrategy = async (strategy: TradingStrategy) => {
    setIsRunningBacktest(true);

    try {
      if (strategy.id === 'fibonacci-retracement') {
        // Run real Fibonacci backtest
        console.log('🔢 Running real Fibonacci backtest...');

        // Use REAL-TIME dates like Fibonacci agent (recent data that exists)
        const endDate = new Date().toISOString();
        const startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(); // Last 30 days

        const response = await fetch('/api/backtest/fibonacci', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            startDate,
            endDate,
            symbol: 'ADAUSD'
          })
        });

        if (response.ok) {
          const realResults = await response.json();
          console.log('✅ Real Fibonacci backtest completed:', realResults);
          setBacktestResults(realResults);

          // Update strategy card with REAL data
          setRealStrategyData(prev => ({
            ...prev,
            [strategy.id]: {
              winRate: realResults.winRate || 0,
              totalTrades: realResults.totalTrades || 0,
              totalPnl: realResults.totalNetPnl || 0,
              maxDrawdown: realResults.maxDrawdown || 0,
              lastUpdated: new Date().toISOString(),
              isRealData: true
            }
          }));
        } else {
          console.warn('⚠️ Real backtest failed, using sample data');
          const results = strategyResults[strategy.id as keyof typeof strategyResults];
          setBacktestResults(results);

          // Update card with fallback data
          setRealStrategyData(prev => ({
            ...prev,
            [strategy.id]: {
              winRate: results.winRate || 0,
              totalTrades: results.totalTrades || 0,
              totalPnl: results.totalNetPnl || 0,
              maxDrawdown: results.maxDrawdown || 0,
              lastUpdated: new Date().toISOString(),
              isRealData: false,
              isFallback: true
            }
          }));
        }
      } else if (strategy.id === 'multi-timeframe-ada') {
        // Run real Multi-Timeframe backtest
        console.log('📊 Running real Multi-Timeframe backtest...');

        // NO DATES - Use count approach like Fibonacci agent
        const response = await fetch('/api/backtest/multi-timeframe', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            symbol: 'ADAUSD'
          })
        });

        if (response.ok) {
          const realResults = await response.json();
          console.log('✅ Real Multi-Timeframe backtest completed:', realResults);
          setBacktestResults(realResults);

          // Update strategy card with REAL data
          setRealStrategyData(prev => ({
            ...prev,
            [strategy.id]: {
              winRate: realResults.winRate || 0,
              totalTrades: realResults.totalTrades || 0,
              totalPnl: realResults.totalNetPnl || 0,
              maxDrawdown: realResults.maxDrawdown || 0,
              lastUpdated: new Date().toISOString(),
              isRealData: true
            }
          }));
        } else {
          console.warn('⚠️ Real backtest failed, using sample data');
          const results = strategyResults[strategy.id as keyof typeof strategyResults];
          setBacktestResults(results);

          // Update card with fallback data
          setRealStrategyData(prev => ({
            ...prev,
            [strategy.id]: {
              winRate: results.winRate || 0,
              totalTrades: results.totalTrades || 0,
              totalPnl: results.totalNetPnl || 0,
              maxDrawdown: results.maxDrawdown || 0,
              lastUpdated: new Date().toISOString(),
              isRealData: false,
              isFallback: true
            }
          }));
        }
      } else {
        // Use sample data for other strategies
        const results = strategyResults[strategy.id as keyof typeof strategyResults];
        setBacktestResults(results);

        // Update strategy card with sample data (ONLY as fallback)
        setRealStrategyData(prev => ({
          ...prev,
          [strategy.id]: {
            winRate: results.winRate || 0,
            totalTrades: results.totalTrades || 0,
            totalPnl: results.totalNetPnl || 0,
            maxDrawdown: results.maxDrawdown || 0,
            lastUpdated: new Date().toISOString(),
            isRealData: false,
            isFallback: true
          }
        }));
      }
    } catch (error) {
      console.error('❌ Backtest error:', error);
      // Fallback to sample data
      const results = strategyResults[strategy.id as keyof typeof strategyResults];
      setBacktestResults(results);

      // Update strategy card with error fallback data
      setRealStrategyData(prev => ({
        ...prev,
        [strategy.id]: {
          winRate: results.winRate || 0,
          totalTrades: results.totalTrades || 0,
          totalPnl: results.totalNetPnl || 0,
          maxDrawdown: results.maxDrawdown || 0,
          lastUpdated: new Date().toISOString(),
          isRealData: false,
          isError: true
        }
      }));
    }

    setIsRunningBacktest(false);
  };

  // Helper functions for strategy display
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800 border-green-200';
      case 'beta': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'coming-soon': return 'bg-gray-100 text-gray-800 border-gray-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'technical': return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'algorithmic': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'hybrid': return 'bg-indigo-100 text-indigo-800 border-indigo-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Page Header with Logo */}
      <PageHeader
        title="Backtesting Dashboard"
        subtitle="Test trading strategies with historical data and analyze performance metrics in real-time"
        showBackButton={true}
        backHref="/dashboard"
      >
        {/* Header Stats */}
        <div className="hidden md:flex items-center gap-6">
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {strategies.filter(s => s.status === 'active').length}
            </div>
            <div className="text-sm text-muted-foreground">Active Strategies</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">
              {selectedStrategy ? '1' : '0'}
            </div>
            <div className="text-sm text-muted-foreground">Running Tests</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600">
              {Object.keys(realStrategyData).filter(id => realStrategyData[id]?.isRealData).length}
            </div>
            <div className="text-sm text-muted-foreground">Live Results</div>
          </div>
        </div>
      </PageHeader>

      <div className="container mx-auto px-6 pt-6 pb-6">
        {/* Status Bar */}
        <div className="flex items-center gap-4 p-4 bg-muted/30 rounded-lg border mb-8">
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span className="text-sm font-medium">System Online</span>
          </div>
          <div className="text-sm text-muted-foreground">
            Real-time data from Kraken API • Last updated: {isMounted ? currentTime : '--:--:--'}
          </div>
          {isRunningBacktest && (
            <div className="flex items-center gap-2 ml-auto">
              <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
              <span className="text-sm font-medium text-blue-600">Running Backtest...</span>
            </div>
          )}
        </div>

        {/* Enhanced Layout: Strategy Selector + Results */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Panel: Strategy Selector */}
          <div className="lg:col-span-1">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span>Trading Strategies</span>
                  <Badge variant="outline" className="text-sm">
                    {strategies.filter(s => s.status === 'active').length} Active
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {strategies.map((strategy) => (
                  <Card
                    key={strategy.id}
                    className={`cursor-pointer transition-all duration-200 hover:shadow-md ${
                      selectedStrategy?.id === strategy.id
                        ? 'ring-2 ring-primary border-primary bg-primary/5'
                        : 'hover:border-primary/50'
                    } ${strategy.status === 'coming-soon' ? 'opacity-60' : ''}`}
                    onClick={() => strategy.status !== 'coming-soon' && handleStrategySelect(strategy)}
                  >
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex items-center gap-2">
                          {strategy.icon}
                          <h3 className="font-semibold text-sm">{strategy.name}</h3>
                        </div>
                        <div className="flex flex-col gap-1">
                          <Badge className={`text-xs ${getStatusColor(strategy.status)}`}>
                            {strategy.status.replace('-', ' ').toUpperCase()}
                          </Badge>
                          {realStrategyData[strategy.id] && (
                            <Badge
                              variant="outline"
                              className={`text-xs ${
                                realStrategyData[strategy.id].isRealData
                                  ? 'bg-green-50 text-green-700 border-green-200'
                                  : realStrategyData[strategy.id].isError
                                    ? 'bg-red-50 text-red-700 border-red-200'
                                    : 'bg-yellow-50 text-yellow-700 border-yellow-200'
                              }`}
                            >
                              {realStrategyData[strategy.id].isRealData
                                ? 'LIVE DATA'
                                : realStrategyData[strategy.id].isError
                                  ? 'API ERROR'
                                  : 'FALLBACK'
                              }
                            </Badge>
                          )}
                        </div>
                      </div>

                      <p className="text-xs text-muted-foreground mb-3 line-clamp-2">
                        {strategy.description}
                      </p>

                      {strategy.status === 'active' && (
                        <div className="grid grid-cols-2 gap-2 text-xs">
                          <div className="text-center p-2 bg-muted/50 rounded">
                            <div className="font-semibold text-green-600">
                              {realStrategyData[strategy.id]
                                ? realStrategyData[strategy.id].winRate.toFixed(1)
                                : strategy.performance.winRate.toFixed(1)
                              }%
                            </div>
                            <div className="text-muted-foreground">Win Rate</div>
                          </div>
                          <div className="text-center p-2 bg-muted/50 rounded">
                            <div className="font-semibold">
                              {realStrategyData[strategy.id]
                                ? realStrategyData[strategy.id].totalTrades
                                : strategy.performance.totalTrades
                              }
                            </div>
                            <div className="text-muted-foreground">Trades</div>
                          </div>
                        </div>
                      )}

                      {strategy.status === 'coming-soon' && (
                        <div className="text-center p-2 bg-muted/50 rounded">
                          <div className="text-sm text-muted-foreground">Coming Soon</div>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ))}
              </CardContent>
            </Card>
          </div>

          {/* Right Panel: Chart + Results */}
          <div className="lg:col-span-2">
            {!selectedStrategy ? (
              <Card className="h-full">
                <CardContent className="flex items-center justify-center h-96">
                  <div className="text-center">
                    <BarChart3 className="w-16 h-16 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-semibold mb-2">Select a Strategy</h3>
                    <p className="text-muted-foreground">
                      Choose a trading strategy from the left panel to run a backtest and view results
                    </p>
                  </div>
                </CardContent>
              </Card>
            ) : (
              <div className="space-y-6">
                {/* Chart Section - Always visible */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <BarChart3 className="w-5 h-5" />
                      ADA Price Chart with{' '}
                      <span className="bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800 bg-clip-text text-transparent font-bold animate-pulse drop-shadow-lg" style={{textShadow: '0 0 10px rgba(147, 51, 234, 0.5)'}}>
                        MISTER
                      </span>{' '}
                      Signals
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="p-0">
                    {backtestResults ? (
                      <ApexTradingChart
                        chartData={backtestResults.chartData}
                        trades={backtestResults.trades}
                        className="w-full"
                      />
                    ) : (
                      <div className="h-96 bg-muted/20 rounded-lg flex items-center justify-center">
                        <div className="text-center">
                          <TrendingUp className="w-12 h-12 text-muted-foreground mx-auto mb-2" />
                          <p className="text-muted-foreground">Run a backtest to see the chart with trading signals</p>
                          <p className="text-xs text-muted-foreground mt-1">
                            Strategy: {selectedStrategy.name} • Timeframe: 15m
                          </p>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* Backtest Results Section */}
                <div className="space-y-4">
                  {/* Strategy Info Header */}
                  <Card>
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between">
                        <div>
                          <h2 className="text-xl font-semibold mb-1">{selectedStrategy.name}</h2>
                          <p className="text-muted-foreground text-sm">{selectedStrategy.description}</p>
                        </div>
                        <div className="flex gap-2">
                          <Badge className={`${getStatusColor(selectedStrategy.status)}`}>
                            {selectedStrategy.status.replace('-', ' ').toUpperCase()}
                          </Badge>
                          <Badge variant="outline" className={`${getTypeColor(selectedStrategy.type)}`}>
                            {selectedStrategy.type.toUpperCase()}
                          </Badge>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Backtest Results */}
                {isRunningBacktest && (
                  <ImprovedBacktestResults
                    results={{
                      ...strategyResults[selectedStrategy.id as keyof typeof strategyResults],
                      strategy: selectedStrategy.name
                    }}
                    isLoading={true}
                  />
                )}

                {backtestResults && !isRunningBacktest && (
                  <ImprovedBacktestResults
                    results={{
                      ...backtestResults,
                      strategy: selectedStrategy.name
                    }}
                    isLoading={false}
                  />
                )}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
