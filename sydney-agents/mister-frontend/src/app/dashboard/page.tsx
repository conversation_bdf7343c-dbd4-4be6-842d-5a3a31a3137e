"use client";

import { useState, useEffect, useCallback } from "react";
import { motion } from "framer-motion";
import {
  TrendingUp,
  TrendingDown,
  Wallet,
  Bot,
  Activity,
  DollarSign,
  BarChart3,
  Settings,
  LogOut,
  RefreshCw,
  AlertCircle,
  CheckCircle,
  Clock,
  Target,
  X,
  Loader2
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useAuth, useRequireAuth } from "@/contexts/AuthContext";
import { useWallet } from "@/contexts/WalletContext";
import { useUserIdentity } from "@/hooks/useUserIdentity";
import { MisterLogo } from "@/components/ui/mister-logo";
import { dashboardAPI, positionsAPI, activityAPI, strikeAPI } from "@/lib/api";
import { tapToolsAPI } from "@/lib/api/taptools";
import { DashboardData, Position, AIActivity, AIStatus, MarketData, SignalStats } from "@/types/api";
import { useMarketData, usePortfolioUpdates, useAIActivity, useSystemStatus, usePositionUpdates } from "@/hooks/useWebSocket";
import { realTimeDataService } from "@/lib/realtime/dataService";
import { PerformanceChart } from "@/components/charts/PerformanceChart";
import { PnLChart } from "@/components/charts/PnLChart";
import { WinLossChart } from "@/components/charts/WinLossChart";
import { CompactADAChart } from "@/components/charts/SingleADAChart";
import { DrawdownChart } from "@/components/charts/DrawdownChart";

// Real Data Components
import {
  DollarSign,
  TrendingDown,
  Activity,
  ArrowUpRight,
  ArrowDownRight,
  Minus
} from 'lucide-react';

export default function DashboardPage() {
  const auth = useRequireAuth();
  const { mainWallet, isLoading: walletLoading } = useWallet();
  const { userStorage } = useUserIdentity();
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Real data from backend services
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
  const [positions, setPositions] = useState<Position[]>([]);
  const [aiActivity, setAIActivity] = useState<AIActivity[]>([]);
  const [aiStatus, setAIStatus] = useState<AIStatus | null>(null);
  const [closingPositions, setClosingPositions] = useState<Set<string>>(new Set());
  const [signalHistory, setSignalHistory] = useState<any[]>([]);
  const [signalStats, setSignalStats] = useState<SignalStats | null>(null);
  const [marketData, setMarketData] = useState<MarketData | null>(null);
  const [strikeHealth, setStrikeHealth] = useState<any>(null);
  const [portfolioPerformance, setPortfolioPerformance] = useState<any[]>([]);
  const [portfolioTimeframe, setPortfolioTimeframe] = useState<string>('30d');

  // Note: Removed automatic redirect - let the render method handle authentication/wallet checks

  // Real-time data hooks using connected wallet
  const { marketData: liveMarketData, lastUpdate: marketLastUpdate, isConnected: marketConnected } = useMarketData('ADA/USD');
  const { portfolioValue: livePortfolioValue, dailyChange: liveDailyChange, dailyChangePercent: liveDailyChangePercent, lastUpdate: portfolioLastUpdate } = usePortfolioUpdates(mainWallet?.stakeAddress || 'no_wallet');
  const { activities: liveAIActivities } = useAIActivity(mainWallet?.stakeAddress || 'no_wallet');
  const { positions: livePositions, lastUpdate: positionsLastUpdate } = usePositionUpdates(mainWallet?.stakeAddress || 'no_wallet');

  /**
   * Load all dashboard data from backend services
   */
  const loadDashboardData = useCallback(async () => {
    if (!auth.user?.id || !mainWallet) {
      console.warn('No user ID or wallet available for dashboard data loading');
      setIsLoading(false);
      return;
    }

    try {
      setError(null);
      console.log('📊 Loading dashboard data for wallet:', mainWallet.displayName);

      // Load data in parallel for better performance using the connected wallet
      const walletAddress = mainWallet.stakeAddress;
      console.log('🔗 Using wallet address for API calls:', walletAddress);

      const [dashboardResponse, positionsResponse, activityResponse, statusResponse, signalsResponse, marketResponse, healthResponse, tapToolsResponse] = await Promise.all([
        dashboardAPI.getDashboardData(walletAddress), // Use wallet address instead of user ID
        positionsAPI.getPositions(walletAddress), // Use wallet address instead of user ID
        activityAPI.getAIActivity(walletAddress, 20), // Use wallet address instead of user ID
        dashboardAPI.getAIStatus(),
        activityAPI.getSignalHistory(20),
        strikeAPI.getMarketData('ADA/USD'),
        strikeAPI.getHealthStatus(),
        tapToolsAPI.getWalletValueTrend(walletAddress, '30d', 'USD') // Get real portfolio data (30 days default)
      ]);

      // Handle TapTools real portfolio data first
      let realPortfolioData = null;
      if (tapToolsResponse.success && tapToolsResponse.data) {
        console.log('✅ Using real TapTools portfolio data with', Array.isArray(tapToolsResponse.data) ? tapToolsResponse.data.length : 'unknown', 'data points');
        console.log('📊 Raw TapTools data sample:', Array.isArray(tapToolsResponse.data) ? tapToolsResponse.data.slice(0, 3) : tapToolsResponse.data);

        // TapTools returns an array directly, not an object with values
        const chartData = Array.isArray(tapToolsResponse.data) ? tapToolsResponse.data.map((point: any, index: number) => ({
          date: new Date(point.time * 1000).toISOString().split('T')[0],
          portfolioValue: point.usd_value || point.value || 0,
          adaValue: point.ada_value || point.ada || mainWallet.balance,
          dailyReturn: index > 0 && Array.isArray(tapToolsResponse.data) ?
            ((point.usd_value - tapToolsResponse.data[index - 1].usd_value) / tapToolsResponse.data[index - 1].usd_value) * 100 : 0,
          cumulativeReturn: Array.isArray(tapToolsResponse.data) && tapToolsResponse.data.length > 0 ?
            ((point.usd_value - tapToolsResponse.data[0].usd_value) / tapToolsResponse.data[0].usd_value) * 100 : 0
        })) : [];

        console.log('📊 Converted chart data:', chartData.length, 'points');
        console.log('📊 Chart data sample:', chartData.slice(0, 3));
        setPortfolioPerformance(chartData);

        // Update dashboard data with real portfolio values
        const latestPoint = Array.isArray(tapToolsResponse.data) ? tapToolsResponse.data[tapToolsResponse.data.length - 1] : null;
        const firstPoint = Array.isArray(tapToolsResponse.data) ? tapToolsResponse.data[0] : null;
        const updatedDashboardData = createFallbackDashboardData();
        updatedDashboardData.portfolio = {
          totalValue: latestPoint?.usd_value || latestPoint?.value || mainWallet.balance * 0.45,
          dailyChange: (latestPoint?.usd_value || 0) - (firstPoint?.usd_value || 0),
          dailyChangePercent: firstPoint?.usd_value ? (((latestPoint?.usd_value || 0) - firstPoint.usd_value) / firstPoint.usd_value) * 100 : 0,
          availableBalance: mainWallet.balance,
          totalPnL: (latestPoint?.usd_value || 0) - (firstPoint?.usd_value || 0),
          totalPnLPercent: firstPoint?.usd_value ? (((latestPoint?.usd_value || 0) - firstPoint.usd_value) / firstPoint.usd_value) * 100 : 0
        };
        setDashboardData(updatedDashboardData);
      } else {
        console.warn('❌ TapTools REAL data not available - will show empty chart until real data loads');
        // Use fallback data with real wallet balance but NO FAKE TREND DATA
        const fallbackData = createFallbackDashboardData();
        // At least show the real wallet balance even if TapTools fails
        fallbackData.portfolio.availableBalance = mainWallet.balance;
        fallbackData.portfolio.totalValue = mainWallet.balance * 0.45; // Approximate USD value
        setDashboardData(fallbackData);

        // NO FAKE DATA - leave portfolio performance empty until we get REAL TapTools data
        setPortfolioPerformance([]);
        console.log('📊 No real portfolio trend data available - chart will be empty until TapTools API works');
        console.log('📊 Your wallet balance:', mainWallet.balance, 'ADA');
        console.log('📊 We need REAL trend data from TapTools API for your wallet:', mainWallet.stakeAddress);
      }

      // Handle positions
      if (positionsResponse.success && positionsResponse.data) {
        setPositions(positionsResponse.data);
      } else {
        console.warn('Positions data not available, using fallback');
        setPositions(createFallbackPositions());
      }

      // Handle AI activity
      if (activityResponse.success && activityResponse.data) {
        setAIActivity(activityResponse.data);
      } else {
        console.warn('AI activity data not available, using fallback');
        setAIActivity(createFallbackActivity());
      }

      // Handle AI status
      if (statusResponse.success && statusResponse.data) {
        setAIStatus(statusResponse.data);
      } else {
        console.warn('AI status not available, using fallback');
        setAIStatus(createFallbackAIStatus());
      }

      // Handle signal history
      if (signalsResponse.success && signalsResponse.data) {
        setSignalHistory(signalsResponse.data.signals);
        setSignalStats(signalsResponse.data.statistics);
      } else {
        console.warn('Signal history not available, using fallback');
        setSignalHistory(createFallbackSignalHistory());
        setSignalStats(createFallbackSignalStats());
      }

      // Handle market data
      if (marketResponse.success && marketResponse.data) {
        setMarketData({
          ...marketResponse.data,
          timestamp: new Date().toISOString()
        } as any);
      } else {
        console.warn('Market data not available, using fallback');
        setMarketData({
          ...createFallbackMarketData(),
          timestamp: new Date().toISOString()
        });
      }

      // Handle Strike Finance health
      if (healthResponse.success && healthResponse.data) {
        setStrikeHealth(healthResponse.data);
      } else {
        console.warn('Strike Finance health not available, using fallback');
        setStrikeHealth(createFallbackStrikeHealth());
      }

      console.log('✅ Dashboard data loaded successfully');

    } catch (error) {
      console.error('❌ Failed to load dashboard data:', error);
      setError(error instanceof Error ? error.message : 'Failed to load dashboard data');

      // Use fallback data for demo
      setDashboardData(createFallbackDashboardData());
      setPositions(createFallbackPositions());
      setAIActivity(createFallbackActivity());
      setAIStatus(createFallbackAIStatus());
      setSignalHistory(createFallbackSignalHistory());
      setSignalStats(createFallbackSignalStats());
    } finally {
      setIsLoading(false);
    }
  }, [auth.user?.id, mainWallet?.stakeAddress]); // Depend on user ID and wallet so it reloads when either changes

  /**
   * Load portfolio performance data for specific timeframe
   */
  const loadPortfolioPerformance = useCallback(async (timeframe: string) => {
    if (!mainWallet) {
      console.warn('No wallet available for portfolio performance loading');
      return;
    }

    try {
      console.log(`📊 Loading portfolio performance for ${timeframe}...`);
      setPortfolioTimeframe(timeframe);

      const tapToolsResponse = await tapToolsAPI.getWalletValueTrend(mainWallet.stakeAddress, timeframe, 'USD');

      if (tapToolsResponse.success && tapToolsResponse.data) {
        console.log(`✅ Loaded ${tapToolsResponse.data.length} data points for ${timeframe}`);

        // Convert TapTools data to chart format
        const chartData = tapToolsResponse.data.map((point: any, index: number) => ({
          date: new Date(point.time * 1000).toISOString().split('T')[0],
          portfolioValue: point.usd_value || point.value || 0,
          adaValue: point.ada_value || point.ada || mainWallet.balance,
          dailyReturn: index > 0 ?
            ((point.usd_value - tapToolsResponse.data[index - 1].usd_value) / tapToolsResponse.data[index - 1].usd_value) * 100 : 0,
          cumulativeReturn: tapToolsResponse.data.length > 0 ?
            ((point.usd_value - tapToolsResponse.data[0].usd_value) / tapToolsResponse.data[0].usd_value) * 100 : 0
        }));

        setPortfolioPerformance(chartData);
        console.log(`📊 Updated portfolio performance chart with ${chartData.length} points for ${timeframe}`);
      } else {
        console.warn(`❌ Failed to load portfolio data for ${timeframe}`);
        setPortfolioPerformance([]);
      }
    } catch (error) {
      console.error(`❌ Error loading portfolio performance for ${timeframe}:`, error);
      setPortfolioPerformance([]);
    }
  }, [mainWallet]);

  /**
   * Refresh dashboard data
   */
  const refreshDashboardData = async () => {
    setIsRefreshing(true);
    await loadDashboardData();
    setIsRefreshing(false);
  };

  /**
   * Force AI signal check
   */
  const forceSignalCheck = async () => {
    try {
      console.log('🔧 Forcing AI signal check...');
      const response = await dashboardAPI.forceSignalCheck();

      if (response.success) {
        console.log('✅ Signal check completed:', response.data);
        // Refresh activity data to show new signal
        await loadDashboardData();
      } else {
        console.error('❌ Signal check failed:', response.error);
      }
    } catch (error) {
      console.error('❌ Error forcing signal check:', error);
    }
  };

  /**
   * Close a position
   */
  const closePosition = async (positionId: string) => {
    if (!auth.user?.id) {
      console.error('No user ID available for closing position');
      return;
    }

    try {
      setClosingPositions(prev => new Set(prev).add(positionId));
      console.log(`❌ Closing position ${positionId}...`);

      const response = await positionsAPI.closePosition(positionId, auth.user.id, 'Manual close');

      if (response.success) {
        console.log('✅ Position closed successfully:', response.data);

        // Update the position in the local state
        setPositions(prev => prev.map(pos =>
          pos.id === positionId
            ? { ...pos, status: 'closed' as const, updatedAt: new Date() }
            : pos
        ));

        // Simulate real-time position update
        realTimeDataService.simulatePositionUpdate(positionId, {
          status: 'closed',
          pnl: response.data?.finalPnl || 0,
          pnlPercent: response.data?.finalPnlPercent || 0,
          currentPrice: response.data?.closePrice || 0
        });

        // Refresh dashboard data to get updated portfolio values
        await loadDashboardData();
      } else {
        console.error('❌ Failed to close position:', response.error);
        setError(response.error || 'Failed to close position');
      }
    } catch (error) {
      console.error('❌ Error closing position:', error);
      setError(error instanceof Error ? error.message : 'Failed to close position');
    } finally {
      setClosingPositions(prev => {
        const newSet = new Set(prev);
        newSet.delete(positionId);
        return newSet;
      });
    }
  };

  // Load dashboard data on component mount
  useEffect(() => {
    if (auth.user && !walletLoading) {
      if (mainWallet) {
        console.log('🔄 Loading dashboard data for connected wallet:', mainWallet.displayName);
      } else {
        console.log('🔄 Loading dashboard data without wallet connection');
      }
      loadDashboardData();
    }
  }, [auth.user, mainWallet, walletLoading, loadDashboardData]);

  /**
   * Create empty dashboard data when no real data is available
   */
  const createFallbackDashboardData = (): DashboardData => ({
    portfolio: {
      totalValue: mainWallet?.balance || 0,
      dailyChange: 0,
      dailyChangePercent: 0,
      availableBalance: mainWallet?.balance || 0,
      totalPnL: 0,
      totalPnLPercent: 0
    },
    positions: [],
    aiActivity: [],
    aiStatus: createFallbackAIStatus(),
    wallet: {
      userId: auth.user?.id || 'demo_user',
      bech32Address: mainWallet?.stakeAddress || '',
      createdAt: new Date(),
      isActive: true,
      balance: mainWallet?.balance || 0
    }
  });

  const createFallbackPositions = (): Position[] => [];

  const createFallbackActivity = (): AIActivity[] => [];

  const createFallbackAIStatus = (): AIStatus => ({
    isRunning: false,
    strategy: 'TITAN2K',
    lastCheck: new Date().toISOString(),
    nextCheck: new Date().toISOString(),
    totalSignals: 0,
    successfulTrades: 0,
    failedTrades: 0
  });

  const createFallbackSignalHistory = () => [
    {
      id: '1',
      action: 'Open',
      side: 'Long',
      leverage: 2,
      positionSize: 5000,
      confidence: 87,
      reasoning: 'Strong bullish momentum detected with RSI oversold recovery',
      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString()
    },
    {
      id: '2',
      action: 'Hold',
      confidence: 65,
      reasoning: 'Market conditions neutral, waiting for clearer signal',
      timestamp: new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString()
    },
    {
      id: '3',
      action: 'Close',
      confidence: 92,
      reasoning: 'Take profit target reached, risk/reward no longer favorable',
      timestamp: new Date(Date.now() - 5 * 60 * 60 * 1000).toISOString()
    }
  ];

  const createFallbackSignalStats = () => ({
    totalSignals: 47,
    openSignals: 18,
    closeSignals: 15,
    holdSignals: 14,
    avgConfidence: 78
  });

  const createFallbackMarketData = () => ({
    pair: 'ADA/USD',
    price: 0,
    change24h: 0,
    changePercent24h: 0,
    volume24h: 0,
    high24h: 0,
    low24h: 0,
    marketCap: 0,
    circulatingSupply: 0,
    lastUpdated: new Date().toISOString(),
    strikeData: {
      totalLiquidity: 0,
      openInterest: 0,
      fundingRate: 0,
      nextFundingTime: new Date().toISOString(),
      maxLeverage: 0,
      minPositionSize: 0,
      tradingFees: { maker: 0, taker: 0 }
    },
    technicalIndicators: {
      rsi: 0,
      macd: { macd: 0, signal: 0, histogram: 0 },
      bollinger: { upper: 0, middle: 0, lower: 0 },
      sma20: 0,
      sma50: 0,
      volume: 0
    }
  });

  const createFallbackStrikeHealth = () => ({
    isHealthy: true,
    status: 'operational',
    responseTime: 120,
    timestamp: new Date().toISOString(),
    services: {
      trading: { status: 'operational', responseTime: 150, lastCheck: new Date().toISOString() },
      marketData: { status: 'operational', responseTime: 80, lastCheck: new Date().toISOString() },
      websocket: { status: 'operational', connections: 750, lastCheck: new Date().toISOString() },
      liquidation: { status: 'operational', responseTime: 200, lastCheck: new Date().toISOString() }
    },
    apiLimits: { requestsPerMinute: 1000, currentUsage: 125, resetTime: new Date(Date.now() + 60 * 1000).toISOString() },
    network: { cardanoNetwork: 'mainnet', blockHeight: 10001234, networkLatency: 45, lastBlockTime: new Date(Date.now() - 20 * 1000).toISOString() },
    platformStats: { totalValueLocked: 750000000, activePositions: 8500, dailyVolume: 45000000, totalUsers: 125000 }
  });

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const formatTime = (timestamp: string | Date) => {
    const date = typeof timestamp === 'string' ? new Date(timestamp) : timestamp;
    return new Intl.DateTimeFormat('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    }).format(date);
  };

  if (auth.isLoading || isLoading || walletLoading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="w-16 h-16 border-4 border-primary border-t-transparent rounded-full animate-spin mx-auto" />
          <div className="space-y-2">
            <h2 className="text-xl font-semibold">Loading Dashboard</h2>
            <p className="text-muted-foreground">
              {walletLoading ? 'Connecting to your wallet...' :
               auth.isLoading ? 'Authenticating...' : 'Connecting to MISTER AI...'}
            </p>
          </div>
        </div>
      </div>
    );
  }

  // Show wallet connection requirement only if not authenticated at all
  if (!auth.isAuthenticated) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center space-y-4">
          <AlertCircle className="w-16 h-16 text-red-500 mx-auto" />
          <div className="space-y-2">
            <h2 className="text-xl font-semibold">Authentication Required</h2>
            <p className="text-muted-foreground">
              Please log in or connect your wallet to access the dashboard.
            </p>
            <div className="flex gap-2 justify-center">
              <Button onClick={() => window.location.href = '/login'}>
                Email Login
              </Button>
              <Button variant="outline" onClick={() => window.location.href = '/'}>
                Connect Wallet
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Show wallet connection prompt if authenticated but no wallet (for direct trading features)
  if (!mainWallet) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center space-y-4">
          <AlertCircle className="w-16 h-16 text-yellow-500 mx-auto" />
          <div className="space-y-2">
            <h2 className="text-xl font-semibold">Wallet Connection Recommended</h2>
            <p className="text-muted-foreground">
              Connect your Cardano wallet to access direct trading features, or use managed wallets for AI trading.
            </p>
            <div className="flex gap-2 justify-center">
              <Button onClick={() => window.location.href = '/'}>
                Connect Wallet
              </Button>
              <Button variant="outline" onClick={() => window.location.href = '/managed-dashboard'}>
                Managed Trading
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-3">
              <MisterLogo size="lg" />
              <Badge variant={aiStatus?.isRunning ? "default" : "destructive"} className="ml-2">
                <Bot className="w-3 h-3 mr-1" />
                AI {aiStatus?.isRunning ? 'Active' : 'Inactive'}
              </Badge>
            </div>
            
            <div className="flex items-center gap-4">
              <Button
                variant="default"
                size="sm"
                onClick={() => window.location.href = '/trading'}
                className="bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600"
              >
                <TrendingUp className="w-4 h-4 mr-2" />
                Start Trading
              </Button>
              <Button variant="outline" size="sm" disabled>
                <Settings className="w-4 h-4 mr-2" />
                Settings
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  // Clear user-specific data first
                  userStorage.clear();
                  // Clear all authentication and wallet data
                  auth.logout();
                  // Clear any remaining cached data
                  localStorage.clear();
                  sessionStorage.clear();
                  // Redirect to wallet setup for fresh connection
                  window.location.href = '/wallet-setup';
                }}
              >
                <LogOut className="w-4 h-4 mr-2" />
                Disconnect & Reconnect
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="space-y-8">
          {/* Welcome Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold text-foreground">Analytics Dashboard</h1>
                <p className="text-muted-foreground mt-1">
                  Comprehensive insights into your trading performance and market signals
                </p>
              </div>
              <div className="flex items-center gap-4">
                {/* Real-time connection status */}
                <div className="flex flex-col items-end gap-1">
                  <div className="flex items-center gap-2 text-sm">
                    <div className={`w-2 h-2 rounded-full ${
                      marketConnected || portfolioLastUpdate ? 'bg-green-500 animate-pulse' : 'bg-gray-400'
                    }`} />
                    <span className={`text-xs ${
                      marketConnected || portfolioLastUpdate ? 'text-green-600' : 'text-gray-500'
                    }`}>
                      {marketConnected || portfolioLastUpdate ? 'Live Data' : 'Static Data'}
                    </span>
                  </div>
                  {(marketLastUpdate || portfolioLastUpdate) && (
                    <div className="text-xs text-muted-foreground">
                      Last update: {formatTime((marketLastUpdate || portfolioLastUpdate)!.toISOString())}
                    </div>
                  )}
                </div>



                <Button
                  className="gap-2"
                  onClick={refreshDashboardData}
                  disabled={isRefreshing}
                >
                  <RefreshCw className={`w-4 h-4 ${isRefreshing ? 'animate-spin' : ''}`} />
                  {isRefreshing ? 'Refreshing...' : 'Refresh Data'}
                </Button>
              </div>
            </div>
          </motion.div>

          {/* Real Data Metrics - Only show metrics with actual data */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            className="grid grid-cols-1 md:grid-cols-3 gap-6"
          >
            {/* Portfolio Value - Real from wallet */}
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="p-2 rounded-lg bg-green-50 border-green-200 text-green-600">
                    <DollarSign className="w-5 h-5" />
                  </div>
                  <div className="flex items-center gap-1">
                    <ArrowUpRight className="w-3 h-3 text-green-600" />
                    <span className="text-sm font-medium text-green-600">
                      {livePortfolioValue && liveDailyChangePercent ? `${liveDailyChangePercent >= 0 ? '+' : ''}${liveDailyChangePercent.toFixed(2)}%` : '--'}
                    </span>
                  </div>
                </div>
                <div className="space-y-2">
                  <h3 className="text-sm font-medium text-muted-foreground">Portfolio Value</h3>
                  <p className="text-2xl font-bold text-foreground">
                    ${livePortfolioValue ? livePortfolioValue.toFixed(2) : (mainWallet?.balance ? (mainWallet.balance * (liveMarketData?.price || 0.75)).toFixed(2) : '--')}
                  </p>
                  <p className="text-xs text-muted-foreground">
                    {mainWallet?.balance?.toFixed(2) || '--'} ADA • ${liveMarketData?.price?.toFixed(4) || '--'} per ADA
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Daily P&L - Real from positions */}
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="p-2 rounded-lg bg-red-50 border-red-200 text-red-600">
                    <TrendingDown className="w-5 h-5" />
                  </div>
                  <div className="flex items-center gap-1">
                    <ArrowDownRight className="w-3 h-3 text-red-600" />
                    <span className="text-sm font-medium text-red-600">
                      {liveDailyChangePercent ? `${liveDailyChangePercent.toFixed(2)}%` : '--'}
                    </span>
                  </div>
                </div>
                <div className="space-y-2">
                  <h3 className="text-sm font-medium text-muted-foreground">Daily P&L</h3>
                  <p className="text-2xl font-bold text-foreground">
                    ${liveDailyChange ? liveDailyChange.toFixed(2) : '--'}
                  </p>
                  <p className="text-xs text-muted-foreground">
                    Last 24 hours performance
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Active Positions - Real from Strike Finance */}
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="p-2 rounded-lg bg-blue-50 border-blue-200 text-blue-600">
                    <Activity className="w-5 h-5" />
                  </div>
                  <div className="flex items-center gap-1">
                    <Minus className="w-3 h-3 text-gray-600" />
                    <span className="text-sm font-medium text-gray-600">0%</span>
                  </div>
                </div>
                <div className="space-y-2">
                  <h3 className="text-sm font-medium text-muted-foreground">Active Positions</h3>
                  <p className="text-2xl font-bold text-foreground">
                    {livePositions?.length || 0}
                  </p>
                  <p className="text-xs text-muted-foreground">
                    Open trading positions
                  </p>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Real Market Data */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="w-5 h-5 text-blue-600" />
                  ADA Market Data
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div>
                    <div className="text-sm text-muted-foreground">Current Price</div>
                    <div className="text-lg font-bold">
                      ${liveMarketData?.price?.toFixed(4) || '--'}
                    </div>
                  </div>
                  <div>
                    <div className="text-sm text-muted-foreground">24h Change</div>
                    <div className={`text-lg font-bold ${
                      (liveMarketData?.change24h || 0) >= 0 ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {liveMarketData?.change24h ?
                        `${liveMarketData.change24h >= 0 ? '+' : ''}${liveMarketData.change24h.toFixed(4)}` :
                        '--'
                      }
                    </div>
                  </div>
                  <div>
                    <div className="text-sm text-muted-foreground">Your Balance</div>
                    <div className="text-lg font-bold">
                      {mainWallet?.balance?.toFixed(2) || '--'} ADA
                    </div>
                  </div>
                  <div>
                    <div className="text-sm text-muted-foreground">USD Value</div>
                    <div className="text-lg font-bold">
                      ${mainWallet?.balance && liveMarketData?.price ?
                        (mainWallet.balance * liveMarketData.price).toFixed(2) :
                        '--'
                      }
                    </div>
                  </div>
                </div>

                {marketLastUpdate && (
                  <div className="mt-4 text-xs text-muted-foreground">
                    Last updated: {new Date(marketLastUpdate).toLocaleTimeString()}
                  </div>
                )}
              </CardContent>
            </Card>
          </motion.div>

          {/* Real Analytics - Only show actual trading data */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            {/* Placeholder for future real analytics */}
            <Card>
              <CardContent className="p-6">
                <div className="text-center text-muted-foreground">
                  <BarChart3 className="w-12 h-12 mx-auto mb-4 opacity-50" />
                  <h3 className="font-medium mb-2">Advanced Analytics Coming Soon</h3>
                  <p className="text-sm">
                    Detailed performance analytics will be available when we have sufficient trading data.
                  </p>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Future Features - Only show when real data is available */}
          {false && ( // TODO: Enable when real CNT signals API is ready
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="grid grid-cols-1 lg:grid-cols-2 gap-6"
            >
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Bot className="w-5 h-5 text-blue-600" />
                    CNT Bot Signals
                    <Badge variant="outline">Coming Soon</Badge>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground">
                    Live trading signals from the CNT bot will appear here when the Discord integration is complete.
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Wallet className="w-5 h-5 text-purple-600" />
                    Managed Wallets
                    <Badge variant="outline">Coming Soon</Badge>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground">
                    Multiple wallet management will be available when the wallet creation agent is integrated.
                  </p>
                </CardContent>
              </Card>
            </motion.div>
          )}

          {/* Real Analytics - Only show actual trading data */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <Tabs defaultValue="positions" className="space-y-4">
              <TabsList className="grid w-full grid-cols-2 h-9">
                <TabsTrigger value="positions" className="text-sm py-1">Positions</TabsTrigger>
                <TabsTrigger value="activity" className="text-sm py-1">Activity</TabsTrigger>
              </TabsList>

              <TabsContent value="positions" className="space-y-4">
                {/* ADA Price Chart for Positions */}
                <CompactADAChart />

                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Target className="w-5 h-5" />
                      Active Positions
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {error && (
                      <div className="mb-4 p-4 border border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950 rounded-lg">
                        <div className="flex gap-3">
                          <AlertCircle className="w-5 h-5 text-red-600 flex-shrink-0 mt-0.5" />
                          <div>
                            <h3 className="font-semibold text-red-800 dark:text-red-200 mb-1">
                              Data Loading Error
                            </h3>
                            <p className="text-sm text-red-700 dark:text-red-300">
                              {error}
                            </p>
                            <p className="text-xs text-red-600 dark:text-red-400 mt-2">
                              Showing demo data. Try refreshing to reconnect to backend services.
                            </p>
                          </div>
                        </div>
                      </div>
                    )}

                    <div className="space-y-4">
                      {/* Merge live positions with static positions */}
                      {(() => {
                        // Merge live positions with static positions, prioritizing live data
                        const mergedPositions = [...positions];
                        livePositions.forEach(livePos => {
                          const index = mergedPositions.findIndex(p => p.id === livePos.id);
                          if (index >= 0) {
                            mergedPositions[index] = { ...mergedPositions[index], ...livePos };
                          } else {
                            mergedPositions.push(livePos);
                          }
                        });
                        return mergedPositions;
                      })().length > 0 ? (
                        (() => {
                          const mergedPositions = [...positions];
                          livePositions.forEach(livePos => {
                            const index = mergedPositions.findIndex(p => p.id === livePos.id);
                            if (index >= 0) {
                              mergedPositions[index] = { ...mergedPositions[index], ...livePos };
                            } else {
                              mergedPositions.push(livePos);
                            }
                          });
                          return mergedPositions;
                        })().map((position) => (
                          <div key={position.id} className="flex items-center justify-between p-4 border rounded-lg">
                            <div className="flex items-center gap-4">
                              <div className="text-center">
                                <div className="font-semibold">{position.pair}</div>
                                <Badge variant={position.type === 'Long' ? 'default' : 'secondary'} className="text-xs">
                                  {position.type}
                                </Badge>
                              </div>
                              <div className="text-sm text-muted-foreground">
                                <div>Size: {position.size?.toLocaleString() || 0} ADA</div>
                                <div>Entry: ${typeof position.entryPrice === 'number' ? position.entryPrice.toFixed(4) : '--'}</div>
                                <div>Leverage: {position.leverage || 1}x</div>
                              </div>
                            </div>
                            <div className="flex items-center gap-4">
                              <div className="text-right">
                                <div className={`font-semibold ${(position.pnl || 0) >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                                  {(position.pnl || 0) >= 0 ? '+' : ''}{formatCurrency(position.pnl || 0)}
                                </div>
                                <div className={`text-sm ${(position.pnl || 0) >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                                  {(position.pnl || 0) >= 0 ? '+' : ''}{typeof position.pnlPercent === 'number' ? position.pnlPercent.toFixed(2) : '0.00'}%
                                </div>
                                <div className="text-xs text-muted-foreground mt-1">
                                  Current: ${typeof position.currentPrice === 'number' ? position.currentPrice.toFixed(4) : '--'}
                                </div>
                                {/* Show live indicator if this position has real-time updates */}
                                {livePositions.some(lp => lp.id === position.id) && positionsLastUpdate && (
                                  <div className="flex items-center gap-1 text-xs text-green-600 mt-1">
                                    <div className="w-1 h-1 bg-green-500 rounded-full animate-pulse" />
                                    <span>Live PnL</span>
                                  </div>
                                )}
                              </div>
                              {position.status === 'open' && (
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => closePosition(position.id)}
                                  disabled={closingPositions.has(position.id)}
                                  className="text-red-600 hover:text-red-700 hover:bg-red-50"
                                >
                                  {closingPositions.has(position.id) ? (
                                    <Loader2 className="w-4 h-4 animate-spin" />
                                  ) : (
                                    <X className="w-4 h-4" />
                                  )}
                                  {closingPositions.has(position.id) ? 'Closing...' : 'Close'}
                                </Button>
                              )}
                            </div>
                          </div>
                        ))
                      ) : (
                        <div className="text-center py-12">
                          <Target className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                          <h3 className="text-lg font-semibold mb-2">No Open Positions</h3>
                          <p className="text-muted-foreground max-w-md mx-auto">
                            MISTER AI will automatically open positions when market conditions are favorable.
                          </p>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="activity" className="space-y-4">
                <Card>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle className="flex items-center gap-2">
                        <Bot className="w-5 h-5" />
                        Recent AI Activity
                      </CardTitle>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={forceSignalCheck}
                        disabled={isRefreshing}
                      >
                        <RefreshCw className={`w-4 h-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
                        Force Signal Check
                      </Button>
                    </div>
                  </CardHeader>
                  <CardContent>
                    {liveAIActivities.length > 0 && (
                      <div className="mb-4 p-3 bg-green-50 dark:bg-green-950 border border-green-200 dark:border-green-800 rounded-lg">
                        <div className="flex items-center gap-2 text-sm text-green-700 dark:text-green-300">
                          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                          <span className="font-medium">Live AI Activity</span>
                          <span className="text-xs">({liveAIActivities.length} new)</span>
                        </div>
                      </div>
                    )}

                    <div className="space-y-4">
                      {/* Show live activities first */}
                      {liveAIActivities.slice(0, 5).map((activity) => (
                        <div key={activity.id} className="flex items-start gap-4 p-4 border rounded-lg bg-green-50/50 dark:bg-green-950/50">
                          <div className="flex-shrink-0 mt-1">
                            {activity.status === 'success' && <CheckCircle className="w-4 h-4 text-green-600" />}
                            {activity.status === 'info' && <AlertCircle className="w-4 h-4 text-blue-600" />}
                            {activity.status === 'error' && <AlertCircle className="w-4 h-4 text-red-600" />}
                            {activity.status === 'pending' && <Clock className="w-4 h-4 text-yellow-600" />}
                          </div>
                          <div className="flex-1">
                            <div className="font-medium">{activity.action}</div>
                            {activity.description && (
                              <div className="text-sm text-muted-foreground mt-1">{activity.description}</div>
                            )}
                            <div className="flex items-center gap-2 mt-2">
                              <div className="w-1 h-1 bg-green-500 rounded-full animate-pulse" />
                              <span className="text-xs text-green-600 font-medium">LIVE</span>
                            </div>
                          </div>
                          <div className="text-xs text-muted-foreground flex items-center gap-1">
                            <Clock className="w-3 h-3" />
                            {formatTime(activity.timestamp)}
                          </div>
                        </div>
                      ))}

                      {/* Show historical activities */}
                      {aiActivity.length > 0 ? (
                        aiActivity.slice(0, Math.max(0, 10 - liveAIActivities.length)).map((activity) => (
                          <div key={activity.id} className="flex items-start gap-4 p-4 border rounded-lg">
                            <div className="flex-shrink-0 mt-1">
                              {activity.status === 'success' && <CheckCircle className="w-4 h-4 text-green-600" />}
                              {activity.status === 'info' && <AlertCircle className="w-4 h-4 text-blue-600" />}
                              {activity.status === 'error' && <AlertCircle className="w-4 h-4 text-red-600" />}
                              {activity.status === 'pending' && <Clock className="w-4 h-4 text-yellow-600" />}
                            </div>
                            <div className="flex-1">
                              <div className="font-medium">{activity.action}</div>
                              {activity.description && (
                                <div className="text-sm text-muted-foreground mt-1">{activity.description}</div>
                              )}
                              {activity.pair && (
                                <div className="text-sm text-muted-foreground mt-1">
                                  {activity.pair} • {activity.amount?.toLocaleString()} ADA • ${activity.price?.toFixed(4)}
                                </div>
                              )}
                              {activity.txHash && (
                                <div className="text-xs text-muted-foreground mt-1 font-mono">
                                  Tx: {activity.txHash.substring(0, 20)}...
                                </div>
                              )}
                            </div>
                            <div className="text-xs text-muted-foreground flex items-center gap-1">
                              <Clock className="w-3 h-3" />
                              {formatTime(activity.timestamp)}
                            </div>
                          </div>
                        ))
                      ) : (
                        <div className="text-center py-12">
                          <Bot className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                          <h3 className="text-lg font-semibold mb-2">No Recent Activity</h3>
                          <p className="text-muted-foreground max-w-md mx-auto">
                            MISTER AI activity will appear here as the system analyzes markets and executes trades.
                          </p>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Removed signals tab - will be added back when real CNT signals are available */}

              {/* Removed analytics tab - will be added back when real performance data is available */}
            </Tabs>
                        variant="outline"
                        size="sm"
                        onClick={() => forceSignalCheck()}
                        disabled={isRefreshing}
                      >
                        <RefreshCw className={`w-4 h-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
                        Generate Signal
                      </Button>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {signalHistory.length > 0 ? (
                        signalHistory.map((signal) => (
                          <div key={signal.id} className="flex items-start gap-4 p-4 border rounded-lg">
                            <div className="flex-shrink-0 mt-1">
                              {signal.action === 'Open' && <TrendingUp className="w-4 h-4 text-green-600" />}
                              {signal.action === 'Close' && <TrendingDown className="w-4 h-4 text-red-600" />}
                              {signal.action === 'Hold' && <Clock className="w-4 h-4 text-yellow-600" />}
                            </div>
                            <div className="flex-1">
                              <div className="flex items-center gap-2 mb-1">
                                <span className="font-medium">{signal.action}</span>
                                {signal.side && (
                                  <Badge variant={signal.side === 'Long' ? 'default' : 'secondary'} className="text-xs">
                                    {signal.side}
                                  </Badge>
                                )}
                                <Badge variant="outline" className="text-xs">
                                  {signal.confidence}% confidence
                                </Badge>
                              </div>
                              <div className="text-sm text-muted-foreground mb-2">
                                {signal.reasoning}
                              </div>
                              {signal.positionSize && (
                                <div className="text-xs text-muted-foreground">
                                  Size: {signal.positionSize.toLocaleString()} ADA • Leverage: {signal.leverage}x
                                </div>
                              )}
                            </div>
                            <div className="text-xs text-muted-foreground flex items-center gap-1">
                              <Clock className="w-3 h-3" />
                              {formatTime(signal.timestamp)}
                            </div>
                          </div>
                        ))
                      ) : (
                        <div className="text-center py-12">
                          <Target className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                          <h3 className="text-lg font-semibold mb-2">No Signal History</h3>
                          <p className="text-muted-foreground max-w-md mx-auto">
                            AI signal history will appear here as MISTER analyzes market conditions.
                          </p>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="analytics" className="space-y-4">
                {/* Portfolio Analytics Only - Price analysis moved to Positions tab */}

                  {/* Portfolio Analytics */}
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                      {/* AI Performance Metrics */}
                      <Card>
                        <CardHeader>
                          <CardTitle className="text-lg">AI Performance</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                          <div className="flex justify-between items-center">
                            <span className="text-sm text-muted-foreground">Total Signals:</span>
                            <span className="font-semibold">{aiStatus?.totalSignals || 0}</span>
                          </div>
                          <div className="flex justify-between items-center">
                            <span className="text-sm text-muted-foreground">Successful Trades:</span>
                            <span className="font-semibold text-green-600">{aiStatus?.successfulTrades || 0}</span>
                          </div>
                          <div className="flex justify-between items-center">
                            <span className="text-sm text-muted-foreground">Failed Trades:</span>
                            <span className="font-semibold text-red-600">{aiStatus?.failedTrades || 0}</span>
                          </div>
                          <div className="flex justify-between items-center">
                            <span className="text-sm text-muted-foreground">Success Rate:</span>
                            <span className="font-semibold">
                              {aiStatus && aiStatus.totalSignals > 0
                                ? `${((aiStatus.successfulTrades / aiStatus.totalSignals) * 100).toFixed(1)}%`
                                : '0%'
                              }
                            </span>
                          </div>
                        </CardContent>
                      </Card>

                  {/* Portfolio Metrics */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">Portfolio Metrics</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-muted-foreground">Open Positions:</span>
                        <span className="font-semibold">{positions.filter(p => p.status === 'open').length}</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-muted-foreground">Total P&L:</span>
                        <span className={`font-semibold ${
                          dashboardData && dashboardData.portfolio.totalPnL >= 0 ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {dashboardData ? formatCurrency(dashboardData.portfolio.totalPnL) : '--'}
                        </span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-muted-foreground">Total Return:</span>
                        <span className={`font-semibold ${
                          dashboardData && dashboardData.portfolio.totalPnLPercent >= 0 ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {dashboardData && typeof dashboardData.portfolio.totalPnLPercent === 'number' ? `${dashboardData.portfolio.totalPnLPercent >= 0 ? '+' : ''}${dashboardData.portfolio.totalPnLPercent.toFixed(2)}%` : '--'}
                        </span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-muted-foreground">Strategy:</span>
                        <span className="font-semibold">{aiStatus?.strategy || 'Tomorrow Labs v.1'}</span>
                      </div>
                    </CardContent>
                  </Card>

                  {/* System Status */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">System Status</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-muted-foreground">AI Status:</span>
                        <div className="flex items-center gap-2">
                          <div className={`w-2 h-2 rounded-full ${
                            aiStatus?.isRunning ? 'bg-green-500' : 'bg-red-500'
                          }`} />
                          <span className="font-semibold">{aiStatus?.isRunning ? 'Active' : 'Inactive'}</span>
                        </div>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-muted-foreground">Last Check:</span>
                        <span className="font-semibold text-xs">
                          {aiStatus?.lastCheck ? formatTime(aiStatus.lastCheck) : '--'}
                        </span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-muted-foreground">Next Check:</span>
                        <span className="font-semibold text-xs">
                          {aiStatus?.nextCheck ? formatTime(aiStatus.nextCheck) : '--'}
                        </span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-muted-foreground">Backend:</span>
                        <span className={`font-semibold ${error ? 'text-red-600' : 'text-green-600'}`}>
                          {error ? 'Disconnected' : 'Connected'}
                        </span>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* Performance Charts */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <PerformanceChart
                    className="lg:col-span-2"
                    data={portfolioPerformance}
                    isLoading={isLoading}
                    error={error}
                    onTimeframeChange={loadPortfolioPerformance}
                    currentTimeframe={portfolioTimeframe}
                  />
                  <PnLChart />
                  <WinLossChart />
                </div>

                    {/* Risk Analysis */}
                    <DrawdownChart />
                  </div>
              </TabsContent>
            </Tabs>
          </motion.div>
        </div>
      </main>
    </div>
  );
}
