"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import {
  Wallet,
  <PERSON>,
  Co<PERSON>,
  <PERSON>,
  Eye,
  EyeOff,
  ArrowRight,
  ArrowLeft,
  AlertTriangle,
  Download,
  CheckCircle,
  Bot,
  BarChart3
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { useAuth } from "@/contexts/AuthContext";
// Removed wallet creation imports - using smart contract approach in future
// import { walletAPI } from "@/lib/api/wallet";
// import { CreateWalletResult } from "@/types/api";
import { useUserIdentity } from "@/hooks/useUserIdentity";
import { USER_STORAGE_KEYS } from "@/lib/utils/userStorage";

export default function OnboardingPage() {
  const { user, autoLogin } = useAuth();

  // Enhanced user identification for secure localStorage
  const {
    userStorage,
    isAuthenticated,
    getUserDisplayName,
  } = useUserIdentity();

  const [currentStep, setCurrentStep] = useState(1);
  // Removed wallet creation state - using smart contract approach in future
  // const [walletData, setWalletData] = useState<CreateWalletResult | null>(null);
  // const [isCreating, setIsCreating] = useState(false);
  // const [walletCreated, setWalletCreated] = useState(false);
  // const [mnemonicVisible, setMnemonicVisible] = useState(false);
  // const [mnemonicCopied, setMnemonicCopied] = useState(false);
  const [confirmationChecks, setConfirmationChecks] = useState({
    saved: false,
    secure: false,
    understand: false
  });

  // Load user-specific onboarding progress
  useEffect(() => {
    if (isAuthenticated && userStorage) {
      const savedProgress = userStorage.getItem(USER_STORAGE_KEYS.ONBOARDING_PROGRESS);
      if (savedProgress) {
        try {
          const progress = JSON.parse(savedProgress);
          setCurrentStep(progress.currentStep || 1);
          setConfirmationChecks(progress.confirmationChecks || {
            saved: false,
            secure: false,
            understand: false
          });
          console.log('📋 [SECURE] Loaded onboarding progress for user:', getUserDisplayName());
        } catch (error) {
          console.warn('⚠️ Failed to parse onboarding progress:', error);
        }
      }
    }
  }, [isAuthenticated]); // Removed userStorage from dependencies to prevent infinite loop

  // Save onboarding progress when it changes
  useEffect(() => {
    if (isAuthenticated && userStorage) {
      const progress = {
        currentStep,
        confirmationChecks,
        lastUpdated: new Date().toISOString()
      };
      userStorage.setItem(USER_STORAGE_KEYS.ONBOARDING_PROGRESS, JSON.stringify(progress));
    }
  }, [currentStep, confirmationChecks, isAuthenticated]); // Removed userStorage from dependencies to prevent infinite loop
  const [error, setError] = useState<string | null>(null);

  const steps = [
    { id: 1, title: "Welcome", description: "Get started with MISTER" },
    { id: 2, title: "Connect Wallet", description: "Connect your Cardano wallet" },
    { id: 3, title: "Smart Contract", description: "Future: Agent Vault setup" },
    { id: 4, title: "Trading Setup", description: "Configure your preferences" },
    { id: 5, title: "Complete", description: "Ready to trade" }
  ];

  // Removed wallet creation function - using smart contract approach in future
  // const handleCreateWallet = async () => { ... }

  // Removed mnemonic handling functions - using smart contract approach in future
  // const handleCopyMnemonic = async () => { ... }
  // const handleDownloadBackup = () => { ... }

  const canProceedToConfirmation = () => {
    return Object.values(confirmationChecks).every(check => check);
  };

  const handleFinishOnboarding = () => {
    // Clear any onboarding progress since we're done
    if (isAuthenticated && userStorage) {
      userStorage.removeItem(USER_STORAGE_KEYS.ONBOARDING_PROGRESS);
      console.log('✅ Onboarding completed, cleared progress data');
    }

    // Navigate to main dashboard where user can connect their wallet
    window.location.href = '/dashboard';
  };

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center space-y-8"
          >
            <div className="w-20 h-20 mx-auto bg-gradient-to-br from-primary/20 to-blue-500/20 rounded-2xl flex items-center justify-center">
              <Wallet className="w-10 h-10 text-primary" />
            </div>
            
            <div className="space-y-4">
              <h2 className="text-3xl font-bold text-foreground">Welcome to MISTER</h2>
              <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                Connect your Cardano wallet for AI-powered trading on Strike Finance.
                In the future, we&apos;ll use smart contracts to keep your funds secure while enabling automation.
              </p>
            </div>

            <div className="grid md:grid-cols-3 gap-6 max-w-4xl mx-auto">
              <Card className="text-center">
                <CardContent className="pt-6">
                  <Shield className="w-8 h-8 text-primary mx-auto mb-4" />
                  <h3 className="font-semibold mb-2">Your Wallet</h3>
                  <p className="text-sm text-muted-foreground">Connect your existing Cardano wallet</p>
                </CardContent>
              </Card>

              <Card className="text-center">
                <CardContent className="pt-6">
                  <Bot className="w-8 h-8 text-primary mx-auto mb-4" />
                  <h3 className="font-semibold mb-2">AI Trading</h3>
                  <p className="text-sm text-muted-foreground">Manual trading with AI analysis</p>
                </CardContent>
              </Card>

              <Card className="text-center">
                <CardContent className="pt-6">
                  <CheckCircle className="w-8 h-8 text-primary mx-auto mb-4" />
                  <h3 className="font-semibold mb-2">Future: Agent Vault</h3>
                  <p className="text-sm text-muted-foreground">Smart contract automation coming soon</p>
                </CardContent>
              </Card>
            </div>

            <Button 
              size="lg" 
              onClick={() => setCurrentStep(2)}
              className="min-w-[200px] h-12"
            >
              Get Started
              <ArrowRight className="w-4 h-4 ml-2" />
            </Button>
          </motion.div>
        );

      case 2:
        return (
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center space-y-8"
          >
            <div className="w-20 h-20 mx-auto bg-gradient-to-br from-primary/20 to-blue-500/20 rounded-2xl flex items-center justify-center">
              <Wallet className="w-10 h-10 text-primary" />
            </div>

            <div className="space-y-4">
              <h2 className="text-3xl font-bold text-foreground">Connect Your Wallet</h2>
              <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                Connect your existing Cardano wallet to start trading with MISTER AI analysis.
                Your wallet remains under your control at all times.
              </p>
            </div>

            <Card className="border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-950 max-w-md mx-auto">
              <CardContent className="pt-6">
                <div className="flex gap-3">
                  <Shield className="w-5 h-5 text-blue-600 flex-shrink-0 mt-0.5" />
                  <div>
                    <h3 className="font-semibold text-blue-800 dark:text-blue-200 mb-1">
                      Wallet Connection
                    </h3>
                    <p className="text-sm text-blue-700 dark:text-blue-300">
                      You&apos;ll connect your existing Cardano wallet (Nami, Eternl, etc.) to start trading.
                    </p>
                    <p className="text-xs text-blue-600 dark:text-blue-400 mt-2">
                      Your private keys never leave your wallet - you maintain full control.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <div className="flex gap-4 justify-center">
              <Button
                variant="outline"
                onClick={() => setCurrentStep(1)}
                className="min-w-[120px]"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back
              </Button>
              <Button
                size="lg"
                onClick={() => setCurrentStep(3)}
                className="min-w-[200px] h-12"
              >
                Continue
                <ArrowRight className="w-4 h-4 ml-2" />
              </Button>
            </div>
          </motion.div>
        );

      case 3:
        return (
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="space-y-8"
          >
            <div className="text-center space-y-4">
              <div className="w-20 h-20 mx-auto bg-gradient-to-br from-primary/20 to-blue-500/20 rounded-2xl flex items-center justify-center">
                <Shield className="w-10 h-10 text-primary" />
              </div>

              <h2 className="text-3xl font-bold text-foreground">Future: Agent Vault Smart Contract</h2>
              <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                We&apos;re developing a secure smart contract system that will allow automated trading
                without compromising your wallet security.
              </p>
            </div>

            <div className="max-w-4xl mx-auto space-y-6">
              {/* How Agent Vault Works */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">How Agent Vault Will Work</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid md:grid-cols-2 gap-6">
                    <div className="space-y-3">
                      <h4 className="font-semibold text-primary">1. Create Your Vault</h4>
                      <p className="text-sm text-muted-foreground">
                        You&apos;ll create a personal smart contract vault that holds your trading funds.
                      </p>
                    </div>
                    <div className="space-y-3">
                      <h4 className="font-semibold text-primary">2. Set Permissions</h4>
                      <p className="text-sm text-muted-foreground">
                        Your vault only allows our agent to trade on Strike Finance - nothing else.
                      </p>
                    </div>
                    <div className="space-y-3">
                      <h4 className="font-semibold text-primary">3. Automated Trading</h4>
                      <p className="text-sm text-muted-foreground">
                        The agent trades automatically using your vault funds, with all profits returning to you.
                      </p>
                    </div>
                    <div className="space-y-3">
                      <h4 className="font-semibold text-primary">4. Full Control</h4>
                      <p className="text-sm text-muted-foreground">
                        You can withdraw funds or revoke permissions anytime - you&apos;re always in control.
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Current Status */}
              <Card className="border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-950">
                <CardContent className="pt-6">
                  <div className="flex gap-3">
                    <Bot className="w-5 h-5 text-blue-600 flex-shrink-0 mt-0.5" />
                    <div className="space-y-2">
                      <h3 className="font-semibold text-blue-800 dark:text-blue-200">
                        Current Status: Manual Trading with AI Analysis
                      </h3>
                      <p className="text-sm text-blue-700 dark:text-blue-300">
                        For now, you can connect your wallet and trade manually with AI-powered analysis and recommendations.
                        The smart contract automation will be added in a future update.
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <div className="flex gap-4 justify-center">
                <Button
                  variant="outline"
                  onClick={() => setCurrentStep(2)}
                  className="min-w-[120px]"
                >
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back
                </Button>
                <Button
                  size="lg"
                  onClick={() => setCurrentStep(4)}
                  className="min-w-[200px] h-12"
                >
                  Continue Setup
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Button>
              </div>
            </div>
          </motion.div>
        );

      case 4:
        return (
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="space-y-8"
          >
            <div className="text-center space-y-4">
              <div className="w-20 h-20 mx-auto bg-gradient-to-br from-primary/20 to-blue-500/20 rounded-2xl flex items-center justify-center">
                <Bot className="w-10 h-10 text-primary" />
              </div>

              <h2 className="text-3xl font-bold text-foreground">Trading Setup</h2>
              <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                Configure your trading preferences and understand how MISTER AI will assist your trading.
              </p>
            </div>

            <div className="max-w-2xl mx-auto space-y-6">
              {/* Trading Setup Checklist */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Trading Configuration</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-4">
                    <label className="flex items-start gap-3 cursor-pointer">
                      <input
                        type="checkbox"
                        checked={confirmationChecks.saved}
                        onChange={(e) => setConfirmationChecks(prev => ({ ...prev, saved: e.target.checked }))}
                        className="mt-1 w-4 h-4 text-primary border-gray-300 rounded focus:ring-primary"
                      />
                      <div className="text-sm">
                        <div className="font-medium">I will connect my Cardano wallet</div>
                        <div className="text-muted-foreground">I have a Cardano wallet (Nami, Eternl, etc.) ready to connect</div>
                      </div>
                    </label>

                    <label className="flex items-start gap-3 cursor-pointer">
                      <input
                        type="checkbox"
                        checked={confirmationChecks.secure}
                        onChange={(e) => setConfirmationChecks(prev => ({ ...prev, secure: e.target.checked }))}
                        className="mt-1 w-4 h-4 text-primary border-gray-300 rounded focus:ring-primary"
                      />
                      <div className="text-sm">
                        <div className="font-medium">I understand manual trading</div>
                        <div className="text-muted-foreground">I understand that I&apos;ll manually execute trades with AI analysis and recommendations</div>
                      </div>
                    </label>

                    <label className="flex items-start gap-3 cursor-pointer">
                      <input
                        type="checkbox"
                        checked={confirmationChecks.understand}
                        onChange={(e) => setConfirmationChecks(prev => ({ ...prev, understand: e.target.checked }))}
                        className="mt-1 w-4 h-4 text-primary border-gray-300 rounded focus:ring-primary"
                      />
                      <div className="text-sm">
                        <div className="font-medium">I understand future automation</div>
                        <div className="text-muted-foreground">I understand that smart contract automation will be added in a future update</div>
                      </div>
                    </label>
                  </div>
                </CardContent>
              </Card>

              {/* Trading Summary */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Trading Summary</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">Trading Mode:</span>
                      <span className="text-sm">Manual with AI Analysis</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">AI Strategies:</span>
                      <span className="text-sm">Fibonacci, Multi-Timeframe</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">Future Automation:</span>
                      <span className="text-sm">Agent Vault Smart Contract</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <div className="flex gap-4 justify-center">
                <Button
                  variant="outline"
                  onClick={() => setCurrentStep(3)}
                  className="min-w-[120px]"
                >
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back
                </Button>
                <Button
                  size="lg"
                  onClick={() => setCurrentStep(5)}
                  disabled={!canProceedToConfirmation()}
                  className="min-w-[200px] h-12"
                >
                  Complete Setup
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Button>
              </div>
            </div>
          </motion.div>
        );

      case 5:
        return (
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center space-y-8"
          >
            <div className="w-20 h-20 mx-auto bg-gradient-to-br from-green-500/20 to-emerald-500/20 rounded-2xl flex items-center justify-center">
              <CheckCircle className="w-10 h-10 text-green-600" />
            </div>

            <div className="space-y-4">
              <h2 className="text-3xl font-bold text-foreground">Welcome to MISTER!</h2>
              <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                You&apos;re ready to start trading with AI-powered analysis and recommendations.
                Connect your wallet on the dashboard to begin.
              </p>
            </div>

            <div className="grid md:grid-cols-3 gap-6 max-w-4xl mx-auto">
              <Card className="text-center">
                <CardContent className="pt-6">
                  <Wallet className="w-8 h-8 text-green-600 mx-auto mb-4" />
                  <h3 className="font-semibold mb-2 text-green-600">Connect Wallet</h3>
                  <p className="text-sm text-muted-foreground">Connect your Cardano wallet to start</p>
                </CardContent>
              </Card>

              <Card className="text-center">
                <CardContent className="pt-6">
                  <Bot className="w-8 h-8 text-green-600 mx-auto mb-4" />
                  <h3 className="font-semibold mb-2 text-green-600">AI Analysis</h3>
                  <p className="text-sm text-muted-foreground">Get AI-powered trading recommendations</p>
                </CardContent>
              </Card>

              <Card className="text-center">
                <CardContent className="pt-6">
                  <Shield className="w-8 h-8 text-green-600 mx-auto mb-4" />
                  <h3 className="font-semibold mb-2 text-green-600">Stay Secure</h3>
                  <p className="text-sm text-muted-foreground">Your keys never leave your wallet</p>
                </CardContent>
              </Card>
            </div>

            <div className="space-y-4">
              <Button
                size="lg"
                onClick={handleFinishOnboarding}
                className="min-w-[240px] h-14 text-base font-semibold"
              >
                <BarChart3 className="w-5 h-5 mr-2" />
                Go to Dashboard
              </Button>

              <p className="text-sm text-muted-foreground">
                Connect your wallet and start trading with AI analysis
              </p>
            </div>
          </motion.div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-gradient-to-br from-primary to-blue-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">M</span>
              </div>
              <span className="text-xl font-bold">MISTER</span>
            </div>

            <div className="flex items-center gap-3">
              {user && (
                <Badge variant="outline" className="flex items-center gap-1">
                  <Wallet className="h-3 w-3" />
                  {user.email || user.id || 'Email User'}
                </Badge>
              )}
              <Badge variant="secondary" className="px-3 py-1">
                Step {currentStep} of {steps.length}
              </Badge>
            </div>
          </div>
        </div>
      </header>

      {/* Progress Bar */}
      <div className="bg-muted/30">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <Progress value={(currentStep / steps.length) * 100} className="h-1" />
        </div>
      </div>

      {/* Main Content */}
      <main className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {renderStep()}
      </main>
    </div>
  );
}
