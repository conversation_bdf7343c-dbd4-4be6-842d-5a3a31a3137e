import { NextRequest, NextResponse } from 'next/server';

/**
 * POST /api/chat/tomorrow-labs
 * Tomorrow Labs Network - Master coordinator agent that can access all other agents
 */
export async function POST(request: NextRequest) {
  try {
    const { message, agentType, chatHistory } = await request.json();

    console.log('🚀 Tomorrow Labs Network received message:', {
      message: message.substring(0, 100) + '...',
      agentType,
      historyLength: chatHistory?.length || 0
    });

    // Determine which agent to route to based on the message content and selected agent
    const routedAgent = determineAgent(message, agentType);
    
    console.log(`🎯 Routing to agent: ${routedAgent}`);

    // Call the appropriate Mastra Cloud agent
    const agentResponse = await callMastraAgent(routedAgent, message, chatHistory);

    return NextResponse.json({
      success: true,
      response: agentResponse,
      agentUsed: routedAgent,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Tomorrow Labs Network error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
      response: 'Sorry, I encountered an error. Please try again.'
    }, { status: 500 });
  }
}

/**
 * Determine which agent to use based on message content and user selection
 */
function determineAgent(message: string, selectedAgent: string): string {
  const lowerMessage = message.toLowerCase();

  // If user explicitly selected an agent, respect that choice
  if (selectedAgent && selectedAgent !== 'tomorrow-labs') {
    return selectedAgent;
  }

  // Smart routing based on message content
  if (lowerMessage.includes('fibonacci') || lowerMessage.includes('trading') || lowerMessage.includes('strategy')) {
    return 'fibonacci-agent';
  }
  
  if (lowerMessage.includes('backtest') || lowerMessage.includes('performance') || lowerMessage.includes('crypto')) {
    return 'crypto-backtesting';
  }
  
  if (lowerMessage.includes('code') || lowerMessage.includes('programming') || lowerMessage.includes('help')) {
    return 'sone-agent';
  }

  // Default to Sone for general queries
  return 'sone-agent';
}

/**
 * Call the appropriate Mastra Cloud agent
 */
async function callMastraAgent(agentType: string, message: string, chatHistory: any[]): Promise<string> {
  const MASTRA_CLOUD_URL = 'https://substantial-scarce-magazin.mastra.cloud';
  
  // Map agent types to Mastra Cloud endpoints
  const agentEndpoints = {
    'fibonacci-agent': '/api/agents/fibonacciAgent/generate',
    'crypto-backtesting': '/api/agents/cryptoBacktestingAgent/generate',
    'sone-agent': '/api/agents/soneAgent/generate',
    'tomorrow-labs': '/api/agents/soneAgent/generate' // Default to Sone for coordination
  };

  const endpoint = agentEndpoints[agentType as keyof typeof agentEndpoints] || agentEndpoints['sone-agent'];

  try {
    console.log(`🌐 Calling Mastra Cloud: ${MASTRA_CLOUD_URL}${endpoint}`);

    // Prepare the request payload
    const payload = {
      messages: [
        // Include recent chat history for context
        ...chatHistory.slice(-5).map((msg: any) => ({
          role: msg.role,
          content: msg.content
        })),
        // Add the current message
        {
          role: 'user',
          content: message
        }
      ]
    };

    const response = await fetch(`${MASTRA_CLOUD_URL}${endpoint}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify(payload),
      // Add timeout
      signal: AbortSignal.timeout(30000) // 30 second timeout
    });

    if (!response.ok) {
      throw new Error(`Mastra Cloud responded with status: ${response.status}`);
    }

    const data = await response.json();
    
    // Extract the response content
    let responseContent = '';
    
    if (data.content) {
      responseContent = data.content;
    } else if (data.response) {
      responseContent = data.response;
    } else if (data.message) {
      responseContent = data.message;
    } else if (typeof data === 'string') {
      responseContent = data;
    } else {
      // Try to extract from common response formats
      responseContent = data.choices?.[0]?.message?.content || 
                      data.result?.content || 
                      data.output ||
                      'I received your message but had trouble formatting the response.';
    }

    console.log('✅ Mastra Cloud response received:', {
      agent: agentType,
      responseLength: responseContent.length,
      preview: responseContent.substring(0, 100) + '...'
    });

    return responseContent;

  } catch (error) {
    console.error(`❌ Error calling ${agentType}:`, error);
    
    // Provide helpful fallback responses based on agent type
    const fallbackResponses = {
      'fibonacci-agent': 'I\'m having trouble connecting to the Fibonacci trading agent right now. Please try again in a moment.',
      'crypto-backtesting': 'The backtesting agent is currently unavailable. Please try again later.',
      'sone-agent': 'I\'m experiencing some connectivity issues. Please try your request again.',
      'tomorrow-labs': 'The Tomorrow Labs network is temporarily unavailable. Please try again.'
    };

    return fallbackResponses[agentType as keyof typeof fallbackResponses] || 
           'I\'m having trouble processing your request right now. Please try again.';
  }
}

/**
 * GET /api/chat/tomorrow-labs
 * Get available agents and network status
 */
export async function GET() {
  try {
    const agents = [
      {
        id: 'tomorrow-labs',
        name: 'Tomorrow Labs',
        description: 'Master coordinator agent',
        status: 'online',
        capabilities: ['coordination', 'routing', 'general-ai']
      },
      {
        id: 'fibonacci-agent',
        name: 'Fibonacci Analyst',
        description: 'Trading strategy expert',
        status: 'online',
        capabilities: ['trading', 'fibonacci', 'technical-analysis']
      },
      {
        id: 'crypto-backtesting',
        name: 'Crypto Backtester',
        description: 'Strategy backtesting specialist',
        status: 'online',
        capabilities: ['backtesting', 'performance-analysis', 'optimization']
      },
      {
        id: 'sone-agent',
        name: 'Sone Assistant',
        description: 'General AI assistant',
        status: 'online',
        capabilities: ['coding', 'analysis', 'general-help']
      }
    ];

    return NextResponse.json({
      success: true,
      network: 'Tomorrow Labs',
      agents,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Error getting network status:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to get network status'
    }, { status: 500 });
  }
}
