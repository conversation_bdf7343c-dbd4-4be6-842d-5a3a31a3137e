# Production Environment Variables for Railway
NODE_ENV=production

# API Keys (will be set in Railway dashboard)
TAPTOOLS_API_KEY=WghkJaZlDWYdQFsyt3uiLdTIOYnR5uhO
BLOCKFROST_PROJECT_ID=mainnetKDR7gGfvHy85Mqr4nYtfjoXq7fX8R1Bu
TWITTER_API_URL=https://twitscap-production.up.railway.app

# Discord Configuration
DISCORD_TOKEN=your-discord-token-here
DISCORD_CHANNEL_ID=1329622661831327773

# LLM Configuration
LLM_PROVIDER=openrouter
LLM_MODEL=google/gemini-2.5-flash
LLM_MAX_TOKENS=10000
OPENROUTER_API_KEY=your-openrouter-key-here

# Trading Configuration
TEST_MODE=false
MAX_ADA_TRADE_PERCENTAGE=80
MIN_ADA_RESERVE=10
MIN_CONFIDENCE_THRESHOLD=8
MIN_TRADE_SIZE=5

# Wallet Configuration (will be set securely in Railway)
WALLET_SEED_PHRASE=your-wallet-seed-phrase-here

# Database (Railway will provide this)
DATABASE_URL=postgresql://user:pass@host:port/db
