#!/bin/bash

echo "🚀 Deploying MISTER Trading Bot to Railway..."

# Check if Railway CLI is installed
if ! command -v railway &> /dev/null; then
    echo "❌ Railway CLI not found. Installing..."
    npm install -g @railway/cli
fi

# Login to Railway (if not already logged in)
echo "🔐 Checking Railway authentication..."
railway whoami || railway login

# Create new Railway project
echo "📦 Creating Railway project..."
railway new mister-trading-bot

# Link to the project
railway link mister-trading-bot

# Add PostgreSQL database
echo "🗄️ Adding PostgreSQL database..."
railway add postgresql

# Set environment variables
echo "⚙️ Setting environment variables..."
railway variables set NODE_ENV=production
railway variables set TAPTOOLS_API_KEY=WghkJaZlDWYdQFsyt3uiLdTIOYnR5uhO
railway variables set BLOCKFROST_PROJECT_ID=mainnetKDR7gGfvHy85Mqr4nYtfjoXq7fX8R1Bu
railway variables set TWITTER_API_URL=https://twitscap-production.up.railway.app
railway variables set DISCORD_CHANNEL_ID=1329622661831327773
railway variables set LLM_PROVIDER=openrouter
railway variables set LLM_MODEL=google/gemini-2.5-flash
railway variables set LLM_MAX_TOKENS=10000
railway variables set TEST_MODE=false
railway variables set MAX_ADA_TRADE_PERCENTAGE=80
railway variables set MIN_ADA_RESERVE=10
railway variables set MIN_CONFIDENCE_THRESHOLD=8
railway variables set MIN_TRADE_SIZE=5

echo "⚠️  IMPORTANT: You need to set these sensitive variables manually in Railway dashboard:"
echo "   - DISCORD_TOKEN"
echo "   - OPENROUTER_API_KEY"
echo "   - WALLET_SEED_PHRASE"

# Deploy to Railway
echo "🚀 Deploying to Railway..."
railway up

echo "✅ Deployment initiated! Check Railway dashboard for status."
echo "🌐 Your bot will be available at: https://mister-trading-bot-production.up.railway.app/health"
