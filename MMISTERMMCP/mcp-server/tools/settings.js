import { z } from "zod";
import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";
import dotenv from "dotenv";

// Get the directory name of the current module
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const projectRoot = path.resolve(__dirname, "../../");

// Settings file path
const SETTINGS_FILE = path.join(projectRoot, "settings.json");

// Default settings
const DEFAULT_SETTINGS = {
  general: {
    botEnabled: true,
    autoTradeEnabled: true,
    maxTradesPerDay: 5,
    maxTradeSize: 100,
  },
  portfolioAllocations: {
    ada: 30,
    meme: 15,
    defi: 20,
    major: 15,
    shard: 10,
    new: 10
  },
  tradingLimits: {
    minConfidence: 7,
    minTokenAge: 7,
    minLiquidity: 5000,
    maxImpact: 5,
  },
  riskManagement: {
    dynamicPositionSizing: true,
    stopLossEnabled: true,
    trailingStopEnabled: false,
    timeBasedExitEnabled: true,
    defaultRiskLevel: "medium"
  },
  apiKeys: {
    taptools: process.env.TAP_TOOLS_API_KEY || '',
    openai: process.env.OPENAI_API_KEY || '',
    blockfrost: process.env.BLOCKFROST_PROJECT_ID || '',
  },
  discord: {
    channelId: process.env.DISCORD_CHANNEL_ID || '',
    notifications: {
      trades: true,
      analysis: true,
      errors: true,
    }
  },
};

// Initialize settings file if it doesn't exist
if (!fs.existsSync(SETTINGS_FILE)) {
  fs.writeFileSync(SETTINGS_FILE, JSON.stringify(DEFAULT_SETTINGS, null, 2));
}

// Create a settings manager
const createSettingsManager = () => {
  return {
    /**
     * Get all settings
     */
    getSettings: async () => {
      try {
        if (fs.existsSync(SETTINGS_FILE)) {
          const settingsData = fs.readFileSync(SETTINGS_FILE, 'utf8');
          return JSON.parse(settingsData);
        }
        return DEFAULT_SETTINGS;
      } catch (error) {
        console.error("Error reading settings:", error);
        return DEFAULT_SETTINGS;
      }
    },
    
    /**
     * Update settings
     */
    updateSettings: async (newSettings) => {
      try {
        // Get current settings
        const currentSettings = await this.getSettings();
        
        // Merge with new settings
        const updatedSettings = {
          ...currentSettings,
          ...newSettings
        };
        
        // Write to file
        fs.writeFileSync(SETTINGS_FILE, JSON.stringify(updatedSettings, null, 2));
        
        return updatedSettings;
      } catch (error) {
        console.error("Error updating settings:", error);
        throw error;
      }
    },
    
    /**
     * Update specific section
     */
    updateSection: async (section, newSectionSettings) => {
      try {
        // Get current settings
        const currentSettings = await this.getSettings();
        
        // Update specific section
        const updatedSettings = {
          ...currentSettings,
          [section]: {
            ...currentSettings[section],
            ...newSectionSettings
          }
        };
        
        // Write to file
        fs.writeFileSync(SETTINGS_FILE, JSON.stringify(updatedSettings, null, 2));
        
        return updatedSettings;
      } catch (error) {
        console.error(`Error updating ${section} settings:`, error);
        throw error;
      }
    },
    
    /**
     * Reset settings to default
     */
    resetSettings: async () => {
      try {
        // Write default settings to file
        fs.writeFileSync(SETTINGS_FILE, JSON.stringify(DEFAULT_SETTINGS, null, 2));
        
        return DEFAULT_SETTINGS;
      } catch (error) {
        console.error("Error resetting settings:", error);
        throw error;
      }
    },
    
    /**
     * Apply settings to environment
     */
    applySettings: async () => {
      try {
        // Get current settings
        const settings = await this.getSettings();
        
        // Update .env file with API keys
        const envPath = path.join(projectRoot, ".env");
        let envContent = "";
        
        if (fs.existsSync(envPath)) {
          envContent = fs.readFileSync(envPath, 'utf8');
        }
        
        // Update API keys
        if (settings.apiKeys) {
          const envLines = envContent.split("\n");
          const updatedLines = envLines.map(line => {
            if (line.startsWith("TAP_TOOLS_API_KEY=")) {
              return `TAP_TOOLS_API_KEY=${settings.apiKeys.taptools}`;
            }
            if (line.startsWith("OPENAI_API_KEY=")) {
              return `OPENAI_API_KEY=${settings.apiKeys.openai}`;
            }
            if (line.startsWith("BLOCKFROST_PROJECT_ID=")) {
              return `BLOCKFROST_PROJECT_ID=${settings.apiKeys.blockfrost}`;
            }
            if (line.startsWith("DISCORD_CHANNEL_ID=")) {
              return `DISCORD_CHANNEL_ID=${settings.discord.channelId}`;
            }
            return line;
          });
          
          // Write updated .env file
          fs.writeFileSync(envPath, updatedLines.join("\n"));
          
          // Reload environment variables
          dotenv.config();
        }
        
        return { success: true, message: "Settings applied successfully" };
      } catch (error) {
        console.error("Error applying settings:", error);
        throw error;
      }
    }
  };
};

// Create the settings manager
const settingsManager = createSettingsManager();

// Export the settings tools
export const settingsTools = {
  getSettings: {
    schema: {},
    handler: async () => {
      try {
        const settings = await settingsManager.getSettings();
        return {
          content: [{
            type: "text",
            text: JSON.stringify(settings, null, 2)
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: "text",
            text: `Error retrieving settings: ${error.message}`
          }],
          isError: true
        };
      }
    }
  },
  
  updateGeneralSettings: {
    schema: {
      botEnabled: z.boolean().optional().describe("Enable/disable the trading bot"),
      autoTradeEnabled: z.boolean().optional().describe("Enable/disable automatic trading"),
      maxTradesPerDay: z.number().optional().describe("Maximum number of trades per day"),
      maxTradeSize: z.number().optional().describe("Maximum trade size in ADA")
    },
    handler: async (params) => {
      try {
        const updatedSettings = await settingsManager.updateSection("general", params);
        return {
          content: [{
            type: "text",
            text: JSON.stringify(updatedSettings.general, null, 2)
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: "text",
            text: `Error updating general settings: ${error.message}`
          }],
          isError: true
        };
      }
    }
  },
  
  updatePortfolioAllocations: {
    schema: {
      ada: z.number().optional().describe("ADA allocation percentage"),
      meme: z.number().optional().describe("Meme token allocation percentage"),
      defi: z.number().optional().describe("DeFi token allocation percentage"),
      major: z.number().optional().describe("Major token allocation percentage"),
      shard: z.number().optional().describe("Shard token allocation percentage"),
      new: z.number().optional().describe("New token allocation percentage")
    },
    handler: async (params) => {
      try {
        const updatedSettings = await settingsManager.updateSection("portfolioAllocations", params);
        return {
          content: [{
            type: "text",
            text: JSON.stringify(updatedSettings.portfolioAllocations, null, 2)
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: "text",
            text: `Error updating portfolio allocations: ${error.message}`
          }],
          isError: true
        };
      }
    }
  },
  
  updateTradingLimits: {
    schema: {
      minConfidence: z.number().optional().describe("Minimum confidence score for trades (1-10)"),
      minTokenAge: z.number().optional().describe("Minimum token age in days"),
      minLiquidity: z.number().optional().describe("Minimum liquidity in ADA"),
      maxImpact: z.number().optional().describe("Maximum price impact percentage")
    },
    handler: async (params) => {
      try {
        const updatedSettings = await settingsManager.updateSection("tradingLimits", params);
        return {
          content: [{
            type: "text",
            text: JSON.stringify(updatedSettings.tradingLimits, null, 2)
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: "text",
            text: `Error updating trading limits: ${error.message}`
          }],
          isError: true
        };
      }
    }
  },
  
  updateRiskManagement: {
    schema: {
      dynamicPositionSizing: z.boolean().optional().describe("Enable/disable dynamic position sizing"),
      stopLossEnabled: z.boolean().optional().describe("Enable/disable stop loss"),
      trailingStopEnabled: z.boolean().optional().describe("Enable/disable trailing stop"),
      timeBasedExitEnabled: z.boolean().optional().describe("Enable/disable time-based exit"),
      defaultRiskLevel: z.enum(["low", "medium", "high", "ultra_high"]).optional().describe("Default risk level")
    },
    handler: async (params) => {
      try {
        const updatedSettings = await settingsManager.updateSection("riskManagement", params);
        return {
          content: [{
            type: "text",
            text: JSON.stringify(updatedSettings.riskManagement, null, 2)
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: "text",
            text: `Error updating risk management settings: ${error.message}`
          }],
          isError: true
        };
      }
    }
  },
  
  updateApiKeys: {
    schema: {
      taptools: z.string().optional().describe("TapTools API key"),
      openai: z.string().optional().describe("OpenAI API key"),
      blockfrost: z.string().optional().describe("Blockfrost project ID")
    },
    handler: async (params) => {
      try {
        const updatedSettings = await settingsManager.updateSection("apiKeys", params);
        
        // Apply settings to environment
        await settingsManager.applySettings();
        
        return {
          content: [{
            type: "text",
            text: JSON.stringify({ success: true, message: "API keys updated and applied" }, null, 2)
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: "text",
            text: `Error updating API keys: ${error.message}`
          }],
          isError: true
        };
      }
    }
  },
  
  updateDiscordSettings: {
    schema: {
      channelId: z.string().optional().describe("Discord channel ID"),
      notifications: z.object({
        trades: z.boolean().optional().describe("Enable/disable trade notifications"),
        analysis: z.boolean().optional().describe("Enable/disable analysis notifications"),
        errors: z.boolean().optional().describe("Enable/disable error notifications")
      }).optional().describe("Notification settings")
    },
    handler: async (params) => {
      try {
        const updatedSettings = await settingsManager.updateSection("discord", params);
        
        // Apply settings to environment
        await settingsManager.applySettings();
        
        return {
          content: [{
            type: "text",
            text: JSON.stringify(updatedSettings.discord, null, 2)
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: "text",
            text: `Error updating Discord settings: ${error.message}`
          }],
          isError: true
        };
      }
    }
  },
  
  resetSettings: {
    schema: {},
    handler: async () => {
      try {
        const defaultSettings = await settingsManager.resetSettings();
        
        // Apply settings to environment
        await settingsManager.applySettings();
        
        return {
          content: [{
            type: "text",
            text: JSON.stringify({ success: true, message: "Settings reset to default" }, null, 2)
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: "text",
            text: `Error resetting settings: ${error.message}`
          }],
          isError: true
        };
      }
    }
  }
};
