# 1. Start all services (if not already running)

cd sydney-agents && npm run dev                    # Terminal 1: Mastra system

cd sydney-agents && cd mister-frontend && npm run dev  # Terminal 3: Front-------

cd sydney-agents && node mister-bridge-server.cjs

npx @agentdeskai/browser-tools-server@latest


----
mister ada mcp
cd MMISTERMCP && npm start

https://substantial-scarce-magazin.mastra.cloud/api/agents/strikeAgent/generate