// agent_vault.ak
//
// This is the Aiken smart contract for the "Agent Vault".
//
// Purpose:
// 1. A user (the Owner) deposits funds into this contract.
// 2. This contract gives a designated Agent the permission to spend these funds.
// 3. The Agent's power is restricted: they can ONLY send the funds to the
//    official Strike Finance smart contracts to open/close positions.
// 4. The Owner can withdraw their funds at any time, revoking the Agent's access.
//
// This design provides full automation for the user after a one-time setup,
// without ever exposing the user's private keys to your service.

use aiken/transaction.{ScriptContext, Spend, Transaction}
use aiken/transaction/credential.{VerificationKey}

//================================================================
//--- Constants & Configuration ---
//================================================================
//
// IMPORTANT: You MUST replace these placeholder values with the real ones
// for your system.
//
// To get a VerificationKeyHash (VKH), you can use a tool like `cardano-cli`.
// It's the hash of the public key of a wallet address.

/// The Verification Key Hash of YOUR trading agent's wallet.
/// This is the *only* wallet that will be authorized to execute trades.
const AGENT_VKH: ByteArray =
  "d8b73234d618228999a83852219c831b43983bb1b83130c2a295843b"

/// A list of the official Strike Finance smart contract script hashes.
/// Your agent will ONLY be allowed to send funds to these addresses.
/// This prevents the agent from sending the user's funds anywhere else.
/// You will need to get these addresses from Strike's documentation or by
/// inspecting their transactions on a block explorer.
const STRIKE_CONTRACT_HASHES: List<ByteArray> = [
  // Placeholder for Strike's `openPosition` contract hash
  "e58541289ab794860a0333a64d1f5843284a772626b9a2b534af914b",
  // Placeholder for Strike's `closePosition` contract hash
  "f733a30f3a6081e35a42ea1f66e857738325f05359c82c332213a1a4",
  // Add any other valid Strike contract hashes here
]

//================================================================
//--- Data Types ---
//================================================================

/// The Datum stored on-chain with the user's funds.
/// It simply tracks who the owner of the vault is.
type Datum {
  /// The Verification Key Hash of the user who owns the funds.
  owner: ByteArray,
}

/// The Redeemer defines the actions that can be taken.
/// It's the "intent" or "command" for how to use the funds in the vault.
type Redeemer {
  /// Allows the Agent to spend the funds to trade on Strike.
  AgentTrade,
  /// Allows the Owner to withdraw their funds from the vault.
  UserWithdraw,
}

//================================================================
//--- Validator Logic ---
//================================================================

/// This is the main validator function that runs on-chain.
/// It checks if a requested action (Redeemer) is allowed based on the
/// rules and the context of the transaction.
validator {
  fn agent_vault(datum: Datum, redeemer: Redeemer, context: ScriptContext) -> Bool {
    let tx: Transaction = context.transaction

    when redeemer is {
      // Case 1: The Agent is executing a trade.
      AgentTrade -> {
        // Rule 1: The transaction MUST be signed by the designated Agent.
        let signed_by_agent = tx.extra_signatories[AGENT_VKH]

        // Rule 2: The funds from this vault MUST be sent to a valid Strike contract.
        let outputs_to_strike =
          tx.outputs
            |> list.any(fn(output) {
              when output.address.credential is {
                // We only care about outputs going to other smart contracts
                VerificationKey(_) -> False
                script_hash: aiken/transaction/credential.Script(_) -> {
                  // Check if the destination script hash is in our allowed list
                  STRIKE_CONTRACT_HASHES
                    |> list.any(fn(strike_hash) { strike_hash == script_hash.hash })
                }
              }
            })

        // The action is valid ONLY if both rules are met.
        signed_by_agent && outputs_to_strike
      }

      // Case 2: The User (Owner) is withdrawing their funds.
      UserWithdraw -> {
        // Rule 1: The transaction MUST be signed by the owner of the vault.
        // The owner's key hash is stored in the Datum.
        tx.extra_signatories[datum.owner]
      }
    }
  }
}
