<!DOCTYPE html>
<html>
<head>
    <title>Force Wallet Refresh</title>
</head>
<body>
    <h1>Force Wallet Refresh</h1>
    <p>This will clear all stored wallet data and refresh the page to force a fresh wallet connection with the correct address from Blockfrost.</p>
    <button onclick="forceRefresh()">Clear Data & Refresh</button>
    <pre id="output"></pre>

    <script>
        function forceRefresh() {
            const output = document.getElementById('output');
            
            function log(message) {
                output.textContent += message + '\n';
                console.log(message);
            }
            
            log('🔄 Forcing wallet refresh...');

            // Clear all wallet-related localStorage
            const keysToRemove = [];
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key && (key.includes('wallet') || key.includes('auth') || key.includes('user'))) {
                    keysToRemove.push(key);
                }
            }

            log('🧹 Clearing stored wallet data: ' + keysToRemove.join(', '));
            keysToRemove.forEach(key => {
                localStorage.removeItem(key);
            });

            log('✅ Stored data cleared. Refreshing page in 2 seconds...');
            log('🔍 This will trigger the Blockfrost address fetching to get the correct address.');

            // Trigger a page refresh
            setTimeout(() => {
                window.location.href = 'http://localhost:3000/trading';
            }, 2000);
        }
    </script>
</body>
</html>